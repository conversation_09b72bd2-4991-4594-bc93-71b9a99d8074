<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Confirm Your Account</title>
    <style>
      body {
        font-family:
          -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
          'Helvetica Neue', Arial, sans-serif;
        line-height: 1.6;
        color: #333;
        max-width: 600px;
        margin: 0 auto;
        padding: 20px;
        background-color: #f9f9f9;
      }
      .email-container {
        background-color: white;
        padding: 40px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
      .header {
        text-align: center;
        margin-bottom: 30px;
      }
      .logo {
        font-size: 28px;
        font-weight: bold;
        color: #6366f1;
        margin-bottom: 10px;
      }
      .title {
        font-size: 24px;
        font-weight: 600;
        color: #1f2937;
        margin-bottom: 10px;
      }
      .subtitle {
        font-size: 16px;
        color: #6b7280;
        margin-bottom: 30px;
      }
      .button-container {
        text-align: center;
        margin: 30px 0;
      }
      .confirm-button {
        display: inline-block;
        background-color: #10b981;
        color: white;
        padding: 12px 24px;
        border-radius: 6px;
        text-decoration: none;
        font-weight: 600;
        font-size: 16px;
      }
      .confirm-button:hover {
        background-color: #059669;
      }
      .instructions {
        font-size: 16px;
        color: #4b5563;
        margin: 20px 0;
        text-align: center;
      }
      .footer {
        margin-top: 40px;
        padding-top: 20px;
        border-top: 1px solid #e5e7eb;
        font-size: 14px;
        color: #6b7280;
        text-align: center;
      }
      .security-note {
        background-color: #fef3c7;
        border: 1px solid #f59e0b;
        border-radius: 6px;
        padding: 15px;
        margin: 20px 0;
        font-size: 14px;
        color: #92400e;
      }
    </style>
  </head>
  <body>
    <div class="email-container">
      <div class="header">
        <div class="logo">SimPLE</div>
        <h1 class="title">Confirm Your Account</h1>
        <p class="subtitle">One last step to activate your SimPLE account</p>
      </div>

      <div class="button-container">
        <a href="{{ .ConfirmationURL }}" class="confirm-button"
          >Confirm Account</a
        >
      </div>

      <p class="instructions">
        Click the button above to confirm your email address and activate your
        SimPLE account.
      </p>

      <div class="security-note">
        <strong>Security Notice:</strong> This confirmation link will expire in
        24 hours. If you didn't create a SimPLE account, please ignore this
        email.
      </div>

      <div class="footer">
        <p>This email was sent from SimPLE</p>
        <p>If you're having trouble, please contact our support team.</p>
      </div>
    </div>
  </body>
</html>
