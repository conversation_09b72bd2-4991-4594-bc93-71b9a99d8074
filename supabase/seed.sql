-- ================================
-- DEVELOPMENT SEED DATA - JKR PIC AND AGENCIES
-- ================================
-- This seed file creates sample data for agencies and JKR personnel
-- SAFETY: Only runs in development environments (local/staging)
-- ================================

-- Production safety check - only run if database name suggests development
DO $$
BEGIN
  -- Check if we're in a development environment
  -- This will prevent accidental execution in production
  IF current_database() NOT IN ('postgres', 'simple-fe-dev', 'simple-fe-local') 
     AND current_database() NOT LIKE '%dev%' 
     AND current_database() NOT LIKE '%test%' 
     AND current_database() NOT LIKE '%staging%' THEN
    RAISE NOTICE 'Skipping seed data - not in development environment (database: %)', current_database();
    RETURN;
  END IF;
  
  RAISE NOTICE 'Running seed data for development environment (database: %)', current_database();
END $$;

-- ================================
-- AGENCIES SEED DATA
-- ================================

-- Federal Government Agencies
INSERT INTO agencies (id, name, state, created_at) VALUES
  (gen_random_uuid(), 'Jabatan Kerja Raya Malaysia (JKR)', NULL, NOW()),
  (gen_random_uuid(), 'Kementerian Kesihatan Malaysia (KKM)', NULL, NOW()),
  (gen_random_uuid(), 'Kementerian Pendidikan Malaysia (KPM)', NULL, NOW()),
  (gen_random_uuid(), 'Kementerian Perumahan dan Kerajaan Tempatan (KPKT)', NULL, NOW()),
  (gen_random_uuid(), 'Kementerian Kemajuan Desa dan Wilayah (KKdW)', NULL, NOW()),
  (gen_random_uuid(), 'Kementerian Perdagangan Dalam Negeri dan Hal Ehwal Pengguna (KPDNHEP)', NULL, NOW()),
  (gen_random_uuid(), 'Kementerian Sains, Teknologi dan Inovasi (MOSTI)', NULL, NOW()),
  (gen_random_uuid(), 'Kementerian Pembangunan Wanita, Keluarga dan Masyarakat (KPWKM)', NULL, NOW())
ON CONFLICT (name) DO NOTHING;

-- State-Level Agencies
INSERT INTO agencies (id, name, state, created_at) VALUES
  -- Kuala Lumpur & Selangor
  (gen_random_uuid(), 'Dewan Bandaraya Kuala Lumpur (DBKL)', 'WP', NOW()),
  (gen_random_uuid(), 'Majlis Bandaraya Petaling Jaya (MBPJ)', 'SL', NOW()),
  (gen_random_uuid(), 'Majlis Bandaraya Subang Jaya (MBSJ)', 'SL', NOW()),
  (gen_random_uuid(), 'Majlis Bandaraya Shah Alam (MBSA)', 'SL', NOW()),
  (gen_random_uuid(), 'Majlis Perbandaran Klang (MPK)', 'SL', NOW()),
  
  -- Johor
  (gen_random_uuid(), 'Majlis Bandaraya Johor Bahru (MBJB)', 'JH', NOW()),
  (gen_random_uuid(), 'Majlis Perbandaran Johor Bahru Tengah (MPJBT)', 'JH', NOW()),
  
  -- Penang
  (gen_random_uuid(), 'Majlis Bandaraya Pulau Pinang (MBPP)', 'PH', NOW()),
  (gen_random_uuid(), 'Majlis Perbandaran Seberang Perai (MPSP)', 'PH', NOW()),
  
  -- Sabah & Sarawak
  (gen_random_uuid(), 'Dewan Bandaraya Kota Kinabalu (DBKK)', 'SB', NOW()),
  (gen_random_uuid(), 'Dewan Bandaraya Kuching Utara (DBKU)', 'SW', NOW()),
  (gen_random_uuid(), 'Dewan Bandaraya Kuching Selatan (DBKS)', 'SW', NOW()),
  
  -- Other States
  (gen_random_uuid(), 'Majlis Bandaraya Alor Setar (MBAS)', 'KD', NOW()),
  (gen_random_uuid(), 'Majlis Bandaraya Ipoh (MBI)', 'PK', NOW()),
  (gen_random_uuid(), 'Majlis Bandaraya Melaka Bersejarah (MBMB)', 'ML', NOW()),
  (gen_random_uuid(), 'Majlis Perbandaran Seremban (MPS)', 'NS', NOW()),
  (gen_random_uuid(), 'Majlis Perbandaran Kuantan (MPK)', 'PH', NOW()),
  (gen_random_uuid(), 'Majlis Perbandaran Kuala Terengganu (MPKT)', 'TR', NOW()),
  (gen_random_uuid(), 'Majlis Perbandaran Kota Bharu (MPKB)', 'KT', NOW()),
  (gen_random_uuid(), 'Majlis Perbandaran Kangar (MPKangar)', 'PL', NOW())
ON CONFLICT (name) DO NOTHING;

-- ================================
-- JKR USERS SEED DATA
-- ================================

-- JKR Admins (Project-level access - can view all projects)
INSERT INTO users (
  id, name, phone_number, email, user_role, admin_access_mode, 
  monitoring_state, contractor_id, onboarding_completed, state, created_at
) VALUES
  (
    gen_random_uuid(), 
    'Datuk Ahmad Zainuddin bin Abdullah', 
    '+60123456789', 
    '<EMAIL>', 
    'admin', 
    'project', 
    NULL, 
    NULL, 
    true, 
    'WP',
    NOW()
  ),
  (
    gen_random_uuid(), 
    'Datin Siti Rahayu binti Mohamed', 
    '+60123456790', 
    '<EMAIL>', 
    'admin', 
    'project', 
    NULL, 
    NULL, 
    true, 
    'SL',
    NOW()
  ),
  (
    gen_random_uuid(), 
    'Ir. Mohd Faizal bin Ismail', 
    '+60123456791', 
    '<EMAIL>', 
    'admin', 
    'project', 
    NULL, 
    NULL, 
    true, 
    'JH',
    NOW()
  );

-- JKR PICs (State-level access - each monitors specific states)
INSERT INTO users (
  id, name, phone_number, email, user_role, admin_access_mode, 
  monitoring_state, contractor_id, onboarding_completed, state, created_at
) VALUES
  -- Kuala Lumpur & Putrajaya
  (
    gen_random_uuid(), 
    'Encik Azman bin Hassan', 
    '+60123456800', 
    '<EMAIL>', 
    'admin', 
    'state', 
    'WP', 
    NULL, 
    true, 
    'WP',
    NOW()
  ),
  (
    gen_random_uuid(), 
    'Puan Norliza binti Ahmad', 
    '+60123456801', 
    '<EMAIL>', 
    'admin', 
    'state', 
    'PW', 
    NULL, 
    true, 
    'PW',
    NOW()
  ),
  
  -- Selangor
  (
    gen_random_uuid(), 
    'Ir. Rahman bin Abdul Malik', 
    '+60123456802', 
    '<EMAIL>', 
    'admin', 
    'state', 
    'SL', 
    NULL, 
    true, 
    'SL',
    NOW()
  ),
  (
    gen_random_uuid(), 
    'Puan Suraya binti Mohd Noor', 
    '+60123456803', 
    '<EMAIL>', 
    'admin', 
    'state', 
    'SL', 
    NULL, 
    true, 
    'SL',
    NOW()
  ),
  
  -- Johor
  (
    gen_random_uuid(), 
    'Encik Iskandar bin Yusof', 
    '+60123456804', 
    '<EMAIL>', 
    'admin', 
    'state', 
    'JH', 
    NULL, 
    true, 
    'JH',
    NOW()
  ),
  
  -- Penang
  (
    gen_random_uuid(), 
    'Puan Lim Siew Chin', 
    '+60123456805', 
    '<EMAIL>', 
    'admin', 
    'state', 
    'PH', 
    NULL, 
    true, 
    'PH',
    NOW()
  ),
  
  -- Perak
  (
    gen_random_uuid(), 
    'Encik Rajesh Kumar a/l Subramaniam', 
    '+60123456806', 
    '<EMAIL>', 
    'admin', 
    'state', 
    'PK', 
    NULL, 
    true, 
    'PK',
    NOW()
  ),
  
  -- Kedah
  (
    gen_random_uuid(), 
    'Encik Mustafa bin Omar', 
    '+60123456807', 
    '<EMAIL>', 
    'admin', 
    'state', 
    'KD', 
    NULL, 
    true, 
    'KD',
    NOW()
  ),
  
  -- Kelantan
  (
    gen_random_uuid(), 
    'Ustaz Abdullah bin Mahmud', 
    '+60123456808', 
    '<EMAIL>', 
    'admin', 
    'state', 
    'KT', 
    NULL, 
    true, 
    'KT',
    NOW()
  ),
  
  -- Terengganu
  (
    gen_random_uuid(), 
    'Encik Zulkifli bin Mohd Ali', 
    '+60123456809', 
    '<EMAIL>', 
    'admin', 
    'state', 
    'TR', 
    NULL, 
    true, 
    'TR',
    NOW()
  ),
  
  -- Pahang
  (
    gen_random_uuid(), 
    'Puan Rohani binti Ibrahim', 
    '+60123456810', 
    '<EMAIL>', 
    'admin', 
    'state', 
    'PN', 
    NULL, 
    true, 
    'PN',
    NOW()
  ),
  
  -- Negeri Sembilan
  (
    gen_random_uuid(), 
    'Encik Ravi a/l Krishnan', 
    '+60123456811', 
    '<EMAIL>', 
    'admin', 
    'state', 
    'NS', 
    NULL, 
    true, 
    'NS',
    NOW()
  ),
  
  -- Malacca
  (
    gen_random_uuid(), 
    'Puan Aminah binti Zainal', 
    '+60123456812', 
    '<EMAIL>', 
    'admin', 
    'state', 
    'ML', 
    NULL, 
    true, 
    'ML',
    NOW()
  ),
  
  -- Perlis
  (
    gen_random_uuid(), 
    'Encik Hafiz bin Abdul Rahman', 
    '+60123456813', 
    '<EMAIL>', 
    'admin', 
    'state', 
    'PL', 
    NULL, 
    true, 
    'PL',
    NOW()
  ),
  
  -- Sabah
  (
    gen_random_uuid(), 
    'Encik Felix Juntai anak Robert', 
    '+60123456814', 
    '<EMAIL>', 
    'admin', 
    'state', 
    'SB', 
    NULL, 
    true, 
    'SB',
    NOW()
  ),
  (
    gen_random_uuid(), 
    'Puan Stephanie Wong Kim Fong', 
    '+60123456815', 
    '<EMAIL>', 
    'admin', 
    'state', 
    'SB', 
    NULL, 
    true, 
    'SB',
    NOW()
  ),
  
  -- Sarawak
  (
    gen_random_uuid(), 
    'Encik James Liaw Chung Ming', 
    '+60123456816', 
    '<EMAIL>', 
    'admin', 
    'state', 
    'SW', 
    NULL, 
    true, 
    'SW',
    NOW()
  ),
  (
    gen_random_uuid(), 
    'Puan Sarah binti Awang', 
    '+60123456817', 
    '<EMAIL>', 
    'admin', 
    'state', 
    'SW', 
    NULL, 
    true, 
    'SW',
    NOW()
  ),
  
  -- Labuan
  (
    gen_random_uuid(), 
    'Encik Michael Wong Yee Loong', 
    '+60123456818', 
    '<EMAIL>', 
    'admin', 
    'state', 
    'LBN', 
    NULL, 
    true, 
    'LBN',
    NOW()
  );

-- ================================
-- COMPLETION NOTICE
-- ================================
DO $$
BEGIN
  RAISE NOTICE '✅ Seed data successfully created:';
  RAISE NOTICE '   📋 Government agencies (federal and state-level)';
  RAISE NOTICE '   👥 JKR Admin users (project-level access)';
  RAISE NOTICE '   🗺️  JKR PIC users (state-level access for all Malaysian states)';
  RAISE NOTICE '   🔒 Production-safe (only runs in development environments)';
  RAISE NOTICE '';
  RAISE NOTICE 'Use these accounts to test project creation and state-based access control.';
END $$;
