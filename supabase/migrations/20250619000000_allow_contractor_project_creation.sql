-- Migration: Allow contractors to create projects
-- Date: 2025-06-19
-- Description: Modify database constraints and triggers to allow contractors to create and manage projects

-- ================================
-- UPDATE CHECK CONSTRAINT
-- ================================
-- Drop the existing constraint
ALTER TABLE users DROP CONSTRAINT IF EXISTS chk_admin_mode_validity;

-- Add the updated constraint that allows contractors to have project admin access
ALTER TABLE users ADD CONSTRAINT chk_admin_mode_validity 
CHECK (
  (user_role = 'admin' AND admin_access_mode IS NOT NULL) 
  OR 
  (user_role = 'contractor' AND (admin_access_mode IS NULL OR admin_access_mode = 'project'))
  OR 
  (user_role NOT IN ('admin', 'contractor') AND admin_access_mode IS NULL)
);

-- ================================
-- UPDATE TRIGGER FUNCTION
-- ================================
-- Replace the existing trigger function to allow contractors to be project admins
CREATE OR REPLACE FUNCTION enforce_project_admin_mode() RETURNS trigger AS $$
BEGIN
  -- Allow project admin assignment if user has admin_access_mode = 'project' 
  -- OR if user is a contractor (for project creation purposes)
  IF NEW.role = 'admin' THEN
    IF NOT EXISTS (
      SELECT 1 FROM users 
      WHERE id = NEW.user_id 
      AND (admin_access_mode = 'project' OR user_role = 'contractor')
    ) THEN
      RAISE EXCEPTION 'User % is not authorized to be a project admin', NEW.user_id;
    END IF;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- ================================
-- ADD HELPER FUNCTIONS
-- ================================
-- Function to grant project creation permissions to contractors
CREATE OR REPLACE FUNCTION grant_contractor_project_permissions(user_id_param uuid)
RETURNS void AS $$
BEGIN
  UPDATE users 
  SET admin_access_mode = 'project', 
      updated_at = now()
  WHERE id = user_id_param 
    AND user_role = 'contractor' 
    AND admin_access_mode IS NULL;
END;
$$ LANGUAGE plpgsql;

-- Function to check if user can create projects
CREATE OR REPLACE FUNCTION can_user_create_projects(user_id_param uuid)
RETURNS boolean AS $$
DECLARE
  user_record RECORD;
BEGIN
  SELECT user_role, admin_access_mode INTO user_record
  FROM users 
  WHERE id = user_id_param;
  
  IF NOT FOUND THEN
    RETURN FALSE;
  END IF;
  
  RETURN (
    user_record.user_role = 'admin' OR
    (user_record.user_role = 'contractor' AND user_record.admin_access_mode = 'project')
  );
END;
$$ LANGUAGE plpgsql;
