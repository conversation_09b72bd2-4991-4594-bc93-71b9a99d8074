-- Migration: Add appointed_oem_competent_firm column to contractors table
-- This field is required for NON_COMPETENT_FIRM company types to specify 
-- which OEM or competent firm they have been appointed by

-- Add the appointed_oem_competent_firm column to the contractors table
ALTER TABLE contractors 
ADD COLUMN appointed_oem_competent_firm TEXT;

-- Add a comment to document the purpose of this column
COMMENT ON COLUMN contractors.appointed_oem_competent_firm IS 
'Name of the OEM or competent firm that has appointed this non-competent firm. Required for NON_COMPETENT_FIRM contractor types.';

-- Optional: Add a check constraint to ensure the field is populated for NON_COMPETENT_FIRM types
-- Note: This constraint is commented out as it might be better handled at the application level
-- to provide better error messages and user experience
/*
ALTER TABLE contractors 
ADD CONSTRAINT check_appointed_firm_for_non_competent 
CHECK (
  (contractor_type != 'NON_COMPETENT_FIRM') OR 
  (contractor_type = 'NON_COMPETENT_FIRM' AND appointed_oem_competent_firm IS NOT NULL AND LENGTH(TRIM(appointed_oem_competent_firm)) >= 2)
);
*/
