-- Add status and operation_type columns to maintenance_logs table
-- This migration adds proper type constraints for maintenance logging

-- Create enum type for operation types
DO $$ BEGIN 
    CREATE TYPE operation_type_enum AS ENUM ('daily logs', 'second schedule', 'mantrap'); 
EXCEPTION 
    WHEN duplicate_object THEN null; 
END $$;

-- Create enum type for maintenance status
DO $$ BEGIN 
    CREATE TYPE maintenance_status AS ENUM ('fully function', 'partially function', 'broken'); 
EXCEPTION 
    WHEN duplicate_object THEN null; 
END $$;

-- Add the status column to maintenance_logs table
ALTER TABLE maintenance_logs 
ADD COLUMN status maintenance_status NOT NULL DEFAULT 'fully function';

-- Add the operation_type column with proper enum constraint
ALTER TABLE maintenance_logs 
ADD COLUMN operation_type operation_type_enum NOT NULL DEFAULT 'daily logs';

-- Add indexes for better query performance
CREATE INDEX idx_maintenance_logs_status ON maintenance_logs(status);
CREATE INDEX idx_maintenance_logs_operation_type_enum ON maintenance_logs(operation_type);

-- Add comments to document the columns
COMMENT ON COLUMN maintenance_logs.status IS 'Status of the equipment after maintenance: fully function, partially function, or broken';
COMMENT ON COLUMN maintenance_logs.operation_type IS 'Type of maintenance operation: daily logs, second schedule, or mantrap';
