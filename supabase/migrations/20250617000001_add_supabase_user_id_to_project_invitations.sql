-- Add supabase_user_id field to project_invitations table
-- This stores the Supabase Auth user ID for the invited user

ALTER TABLE project_invitations 
ADD COLUMN supabase_user_id uuid REFERENCES auth.users(id) ON DELETE SET NULL;

-- Add index for faster lookups
CREATE INDEX idx_project_invitations_supabase_user_id ON project_invitations(supabase_user_id);

-- Add comment for documentation
COMMENT ON COLUMN project_invitations.supabase_user_id IS 'Supabase Auth user ID for the invited user, used for linking Supabase auth to project invitations';
