-- Add optional state column to users table
-- This column is for admin users to specify their assigned state for filtering and organization
-- It's separate from monitoring_state and can be used for additional state-based features

ALTER TABLE users ADD COLUMN state state_code;

-- Add comment to document the purpose of this column
COMMENT ON COLUMN users.state IS 'Optional state assignment for admin users - used for organization and filtering purposes';