'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
  Building2,
  GalleryVerticalEnd,
  Home,
  Info,
  Mail,
  Menu,
  Users,
  X,
} from 'lucide-react';
import Link from 'next/link';
import { useState } from 'react';

interface LandingPageHeaderProps {
  translations: {
    'nav.home': string;
    'nav.about': string;
    'nav.contact': string;
    adminLogin: string;
    contractorLogin: string;
  };
}

export function LandingPageHeader({ translations: t }: LandingPageHeaderProps) {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  return (
    <header className="fixed top-0 w-full z-50 bg-white/95 backdrop-blur-md border-b border-gray-200/50 shadow-sm">
      <div className="container mx-auto px-4 sm:px-6 py-3 sm:py-4">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <div className="flex items-center gap-4 sm:gap-8">
            <Link
              href="/"
              className="flex items-center gap-2 font-bold text-lg sm:text-xl"
            >
              <div className="bg-primary text-primary-foreground flex size-8 items-center justify-center rounded-lg">
                <GalleryVerticalEnd className="size-4 sm:size-5" />
              </div>
              <span className="hidden xs:block">SimPLE</span>
            </Link>

            {/* Desktop Navigation Links */}
            <nav className="hidden lg:flex items-center gap-6">
              <Button asChild variant="ghost" size="sm" className="gap-2">
                <Link href="#home">
                  <Home className="w-4 h-4" />
                  {t['nav.home']}
                </Link>
              </Button>
              <Button asChild variant="ghost" size="sm" className="gap-2">
                <Link href="#about">
                  <Info className="w-4 h-4" />
                  {t['nav.about']}
                </Link>
              </Button>
              <Button asChild variant="ghost" size="sm" className="gap-2">
                <Link href="#contact">
                  <Mail className="w-4 h-4" />
                  {t['nav.contact']}
                </Link>
              </Button>
            </nav>
          </div>

          {/* Desktop Action Buttons */}
          <div className="hidden sm:flex items-center gap-2 sm:gap-3">
            <Button
              asChild
              variant="outline"
              size="sm"
              className="gap-2 text-xs sm:text-sm px-3 sm:px-4"
            >
              <Link href="/admin/login">
                <Building2 className="w-3 h-3 sm:w-4 sm:h-4" />
                <span className="hidden md:inline">{t.adminLogin}</span>
                <span className="md:hidden">Admin</span>
              </Link>
            </Button>
            <Button
              asChild
              size="sm"
              className="gap-2 text-xs sm:text-sm px-3 sm:px-4"
            >
              <Link href="/contractor/login">
                <Users className="w-3 h-3 sm:w-4 sm:h-4" />
                <span className="hidden md:inline">{t.contractorLogin}</span>
                <span className="md:hidden">Contractor</span>
              </Link>
            </Button>
          </div>

          {/* Mobile Menu Button */}
          <div className="sm:hidden">
            <Button variant="ghost" size="sm" onClick={toggleMobileMenu}>
              {mobileMenuOpen ? (
                <X className="w-5 h-5" />
              ) : (
                <Menu className="w-5 h-5" />
              )}
              <span className="sr-only">Toggle menu</span>
            </Button>
          </div>
        </div>

        {/* Mobile Navigation Menu */}
        {mobileMenuOpen && (
          <div className="sm:hidden mt-4 pb-4 border-t border-gray-200/50">
            <nav className="flex flex-col gap-2 mt-4">
              <Button
                asChild
                variant="ghost"
                className="gap-2 justify-start w-full"
                onClick={() => setMobileMenuOpen(false)}
              >
                <Link href="#home">
                  <Home className="w-4 h-4" />
                  {t['nav.home']}
                </Link>
              </Button>
              <Button
                asChild
                variant="ghost"
                className="gap-2 justify-start w-full"
                onClick={() => setMobileMenuOpen(false)}
              >
                <Link href="#about">
                  <Info className="w-4 h-4" />
                  {t['nav.about']}
                </Link>
              </Button>
              <Button
                asChild
                variant="ghost"
                className="gap-2 justify-start w-full"
                onClick={() => setMobileMenuOpen(false)}
              >
                <Link href="#contact">
                  <Mail className="w-4 h-4" />
                  {t['nav.contact']}
                </Link>
              </Button>
              <div className="flex flex-col gap-2 mt-4 pt-4 border-t border-gray-200">
                <Button
                  asChild
                  variant="outline"
                  className="gap-2 justify-start w-full"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  <Link href="/admin/login">
                    <Building2 className="w-4 h-4" />
                    {t.adminLogin}
                  </Link>
                </Button>
                <Button
                  asChild
                  className="gap-2 justify-start w-full"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  <Link href="/contractor/login">
                    <Users className="w-4 h-4" />
                    {t.contractorLogin}
                  </Link>
                </Button>
              </div>
            </nav>
          </div>
        )}
      </div>
    </header>
  );
}
