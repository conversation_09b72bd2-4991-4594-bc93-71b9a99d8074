'use client';

import * as React from 'react';
import { cn } from '@/lib/utils';

interface SectionHeaderProps {
  number?: number;
  title: string;
  description?: string;
  className?: string;
  variant?: 'default' | 'compact';
}

export function SectionHeader({
  number,
  title,
  description,
  className,
  variant = 'default',
}: SectionHeaderProps) {
  return (
    <div
      className={cn(
        'flex items-center',
        variant === 'default' ? 'mb-6' : 'mb-4',
        className,
      )}
    >
      {number && (
        <div
          className={cn(
            'bg-muted rounded-full flex items-center justify-center mr-3',
            variant === 'default' ? 'w-8 h-8' : 'w-6 h-6',
          )}
        >
          <span
            className={cn(
              'text-muted-foreground font-semibold',
              variant === 'default' ? 'text-sm' : 'text-xs',
            )}
          >
            {number}
          </span>
        </div>
      )}
      <div className="flex-1">
        <h2
          className={cn(
            'font-semibold text-foreground',
            variant === 'default' ? 'text-xl' : 'text-lg',
          )}
        >
          {title}
        </h2>
        {description && (
          <p className="text-sm text-muted-foreground mt-1">{description}</p>
        )}
      </div>
    </div>
  );
}
