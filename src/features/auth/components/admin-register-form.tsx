'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  useAuthTranslations,
  useValidationTranslations,
} from '@/hooks/use-translations';
import { cn } from '@/lib/utils';
import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2 } from 'lucide-react';
import Link from 'next/link';
import * as React from 'react';
import { useForm, type SubmitHandler } from 'react-hook-form';
import { toast } from 'sonner';
import { useSignUp } from '../hooks/use-auth';
import { createRegisterSchema, type RegisterFormValues } from '../schemas';

interface AdminRegisterFormProps extends React.HTMLAttributes<HTMLDivElement> {
  className?: string;
}

export function AdminRegisterForm({ ...props }: AdminRegisterFormProps) {
  const signUpMutation = useSignUp();
  const auth = useAuthTranslations();
  const validation = useValidationTranslations();

  // Use schema from schemas file
  const registerSchema = createRegisterSchema(validation, auth);
  const form = useForm<RegisterFormValues>({
    resolver: zodResolver(registerSchema),
    defaultValues: {
      email: '',
      password: '',
      confirmPassword: '',
      role: 'admin', // Pre-set to admin role
    },
  });

  const onSubmit: SubmitHandler<RegisterFormValues> = async (values) => {
    try {
      const result = await signUpMutation.mutateAsync({
        email: values.email,
        password: values.password,
        role: 'admin', // Ensure admin role is always used
      });

      // Check if user needs email confirmation
      if (result.user && !result.session) {
        // User created but needs email confirmation
        toast.success(auth('adminRegisterSuccessCheckEmail'));
      } else if (result.user && result.session) {
        // User created and immediately confirmed (edge case)
        toast.success(auth('adminRegisterSuccess'));
      }
    } catch (error: unknown) {
      console.error('Admin registration failed:', error);

      // Handle specific error cases
      const errorMessage =
        error instanceof Error ? error.message : String(error);

      if (errorMessage?.includes('User already registered')) {
        toast.error(auth('userExists'));
      } else if (errorMessage?.includes('Profile creation error')) {
        toast.error(auth('profileCreationError'));
      } else if (errorMessage?.includes('email_address_invalid')) {
        toast.error(validation('email'));
      } else if (errorMessage?.includes('signup_disabled')) {
        toast.error(auth('signupDisabled'));
      } else {
        // Fallback for other errors
        toast.error(errorMessage || auth('registerFailed'));
      }
    }
  };

  const isLoading = signUpMutation.isPending;

  return (
    <div className={cn('flex flex-col gap-6')} {...props}>
      <div className="flex flex-col items-center gap-2 text-center">
        <h1 className="text-2xl font-bold">{auth('adminRegisterTitle')}</h1>
        <p className="text-muted-foreground text-sm text-balance">
          {auth('adminRegisterSubtitle')}
        </p>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {signUpMutation.error && (
            <div className="p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
              {signUpMutation.error.message}
            </div>
          )}

          <div className="grid gap-4">
            {/* Email Field */}
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {auth('email')} <span className="text-red-500">*</span>
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder={auth('emailPlaceholder')}
                      type="email"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Password Fields */}
            <div className="grid gap-4">
              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      {auth('password')} <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input type="password" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="confirmPassword"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      {auth('confirmPassword')}{' '}
                      <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input type="password" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <Button type="submit" className="w-full" disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {auth('creatingAdminAccount')}
                </>
              ) : (
                auth('createAdminAccount')
              )}
            </Button>
            <div className="text-center text-sm">
              {auth('alreadyHaveAdminAccount')}{' '}
              <Link
                href="/admin/login"
                className="underline underline-offset-4"
              >
                {auth('adminSignIn')}
              </Link>
            </div>
          </div>
        </form>
      </Form>
    </div>
  );
}
