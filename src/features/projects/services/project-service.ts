import { uploadToOBS } from '@/lib/obs-upload';
import { supabase } from '@/lib/supabase';
import type { Database } from '@/types/database';
import type { User } from '@supabase/supabase-js';
import type { ProjectFormData } from '../types/project';

// Database type aliases
type ProjectInsert = Database['public']['Tables']['projects']['Insert'];
type PmaCertificateInsert =
  Database['public']['Tables']['pma_certificates']['Insert'];

export interface CreateProjectResponse {
  project: Database['public']['Tables']['projects']['Row'];
  pmaCertificates: Database['public']['Tables']['pma_certificates']['Row'][];
}

/**
 * Upload a file to OBS storage
 */
async function uploadFile(file: File, folder: string): Promise<string> {
  try {
    const url = await uploadToOBS({
      file,
      folder,
    });
    return url;
  } catch (error) {
    throw new Error(
      `Failed to upload file ${file.name}: ${error instanceof Error ? error.message : 'Unknown error'}`,
    );
  }
}

/**
 * Create a new project with all related data
 */
export async function createProject(
  formData: ProjectFormData,
  userId: string,
): Promise<CreateProjectResponse> {
  try {
    // Start a transaction-like operation
    const { data: session } = await supabase.auth.getSession();
    if (!session.session?.user) {
      throw new Error('User not authenticated');
    }

    // Get user profile to check permissions and obtain contractor_id
    const { data: userProfile, error: profileError } = await supabase
      .from('users')
      .select('contractor_id, user_role, admin_access_mode')
      .eq('id', userId)
      .single();

    if (profileError) {
      throw new Error('Failed to get user profile information');
    }

    // Check if user can create projects (admin or contractor)
    const canCreateProject =
      userProfile.user_role === 'admin' ||
      userProfile.user_role === 'contractor';

    if (!canCreateProject) {
      throw new Error('User does not have permission to create projects');
    }

    if (!userProfile?.contractor_id) {
      throw new Error('User profile does not have a contractor ID');
    }

    // Check if the quotation number already exists for this contractor
    const { data: existingProject, error: checkError } = await supabase
      .from('projects')
      .select('id, name')
      .eq('code', formData.code)
      .eq('contractor_id', userProfile.contractor_id)
      .is('deleted_at', null)
      .limit(1);

    if (checkError) {
      throw new Error('Failed to validate quotation number');
    }

    if (existingProject && existingProject.length > 0) {
      throw new Error(
        `A project with quotation number "${formData.code}" already exists for your company: "${existingProject[0].name}". Please check your tender documents and use a different quotation number.`,
      );
    }

    // 1. Create the project first
    const projectData: ProjectInsert = {
      name: formData.name,
      code: formData.code,
      agency_id: formData.agency_id,
      location: formData.location,
      state:
        (formData.state as Database['public']['Tables']['projects']['Row']['state']) ||
        null,
      start_date: formData.start_date,
      end_date: formData.end_date || null,
      status: formData.status || 'pending',
      contractor_id: userProfile.contractor_id, // Automatically set contractor_id
      created_by: userId,
      created_at: new Date().toISOString(),
    };

    const { data: project, error: projectError } = await supabase
      .from('projects')
      .insert(projectData)
      .select()
      .single();

    if (projectError) {
      // Handle specific duplicate code error
      if (
        projectError.code === '23505' &&
        projectError.message?.includes('projects_code_key')
      ) {
        throw new Error(
          `A project with quotation number "${formData.code}" already exists. Please check your tender documents and ensure you're using the correct quotation number.`,
        );
      }

      throw new Error(`Failed to create project: ${projectError.message}`);
    }

    if (!project) {
      throw new Error('Project creation failed: No data returned');
    }

    // 2. Create PMA certificates with file uploads
    const pmaCertificates: Database['public']['Tables']['pma_certificates']['Row'][] =
      [];

    for (const pmaData of formData.pmas.pmas) {
      try {
        // Upload PMA certificate file
        const pmaFileUrl = await uploadFile(
          pmaData.file,
          `projects/${project.id}/pma-certificates`,
        );

        // For now, use the first available admin user as competent person
        // This should be properly implemented with a competent person selection UI
        const { data: adminUsers } = await supabase
          .from('users')
          .select('id')
          .eq('user_role', 'admin')
          .limit(1);

        const competentPersonId = adminUsers?.[0]?.id || userId;

        // Create PMA certificate record
        const pmaCertificateData: PmaCertificateInsert = {
          expiry_date: pmaData.expiry_date,
          location: pmaData.location,
          file_url: pmaFileUrl,
          competent_person_id: competentPersonId,
          project_id: project.id,
          status: 'validating', // Default status
          created_by: userId,
          pma_number: pmaData.pma_number.trim(), // Ensure no leading/trailing spaces
          created_at: new Date().toISOString(),
        };

        const { data: pmaCertificate, error: pmaError } = await supabase
          .from('pma_certificates')
          .insert(pmaCertificateData)
          .select()
          .single();

        if (pmaError) {
          throw new Error(
            `Failed to create PMA certificate: ${pmaError.message}`,
          );
        }

        if (pmaCertificate) {
          pmaCertificates.push(pmaCertificate);
        }
      } catch (error) {
        throw error;
      }
    }

    // 3. Create project user associations
    const projectUserInserts = [];

    // Add the project creator (contractor) to the project with admin role
    projectUserInserts.push({
      project_id: project.id,
      user_id: userId,
      role: 'admin' as const,
      status: 'accepted' as const,
      assigned_date: new Date().toISOString().split('T')[0],
      is_active: true,
      created_by: userId,
      created_at: new Date().toISOString(),
    });

    // Add JKR user if specified
    if (formData.jkr_pic_id) {
      projectUserInserts.push({
        project_id: project.id,
        user_id: formData.jkr_pic_id,
        role: 'admin' as const,
        status: 'accepted' as const,
        assigned_date: new Date().toISOString().split('T')[0],
        is_active: true,
        created_by: userId,
        created_at: new Date().toISOString(),
      });
    }

    // Insert all project user associations
    const { error: projectUserError } = await supabase
      .from('project_users')
      .insert(projectUserInserts);

    if (projectUserError) {
      throw new Error(
        `Failed to create project user associations: ${projectUserError.message}`,
      );
    }

    return {
      project,
      pmaCertificates,
    };
  } catch (error) {
    throw error;
  }
}

/**
 * Get current user profile with auth check
 */
export async function getCurrentUser(): Promise<{
  user: User;
  profile: Database['public']['Tables']['users']['Row'];
} | null> {
  try {
    const { data: session } = await supabase.auth.getSession();
    if (!session.session?.user) {
      return null;
    }

    const { data: profile, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', session.session.user.id)
      .single();

    if (error) {
      return null;
    }

    return {
      user: session.session.user,
      profile,
    };
  } catch (error) {
    throw error;
  }
}

/**
 * Get user's current contractor ID from their profile
 */
export async function getCurrentUserContractorId(): Promise<string | null> {
  try {
    const { data: session } = await supabase.auth.getSession();
    if (!session.session?.user) {
      return null;
    }

    const { data: profile, error } = await supabase
      .from('users')
      .select('contractor_id')
      .eq('id', session.session.user.id)
      .single();

    if (error) {
      return null;
    }

    return profile?.contractor_id || null;
  } catch (error) {
    throw error;
  }
}
