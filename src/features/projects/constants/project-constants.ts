/**
 * Constants used across the projects feature
 * Contains static data for agencies, personnel, and other project-related options
 */

export const AGENCIES = [
  { value: 'JKR', label: 'Jabatan Kerja Raya Malaysia (JKR)' },
  { value: 'KKM', label: 'Kementerian Kesihatan Malaysia (KKM)' },
  { value: 'MOE', label: 'Ministry of Education (MOE)' },
  {
    value: 'MITI',
    label: 'Ministry of International Trade and Industry (MITI)',
  },
  { value: 'MOF', label: 'Ministry of Finance (MOF)' },
] as const;

export const JKR_PICS = [
  { value: 'ahmad.ibrahim', label: '<PERSON> (Senior Engineer)' },
  { value: 'siti.rohani', label: '<PERSON><PERSON> (Project Manager)' },
  { value: 'mohammad.ali', label: '<PERSON> (Technical Lead)' },
  { value: 'fatimah.zahra', label: '<PERSON><PERSON><PERSON> (Operations Manager)' },
  { value: 'hassan.omar', label: '<PERSON> (Quality Assurance)' },
] as const;

export const MALAY<PERSON>AN_STATES = [
  { value: 'JH', label: 'Jo<PERSON>' },
  { value: 'KD', label: 'Kedah' },
  { value: 'KT', label: '<PERSON>lant<PERSON>' },
  { value: 'ML', label: 'Melaka' },
  { value: 'NS', label: 'Negeri Sembilan' },
  { value: 'PH', label: 'Pahang' },
  { value: 'PN', label: 'Pulau Pinang' },
  { value: 'PK', label: 'Perak' },
  { value: 'PL', label: 'Perlis' },
  { value: 'SB', label: 'Sabah' },
  { value: 'SW', label: 'Sarawak' },
  { value: 'SL', label: 'Selangor' },
  { value: 'TR', label: 'Terengganu' },
  { value: 'WP', label: 'W.P. Kuala Lumpur' },
  { value: 'LBN', label: 'W.P. Labuan' },
  { value: 'PW', label: 'W.P. Putrajaya' },
  { value: 'OTH', label: 'Other' },
] as const;

export const PROJECT_STATUSES = [
  { value: 'pending', label: 'Pending' },
  { value: 'active', label: 'Active' },
  { value: 'completed', label: 'Completed' },
  { value: 'cancelled', label: 'Cancelled' },
] as const;

export const PMA_STATUSES = [
  { value: 'validating', label: 'Validating' },
  { value: 'valid', label: 'Valid' },
  { value: 'invalid', label: 'Invalid' },
] as const;

// File upload constraints
export const FILE_UPLOAD_LIMITS = {
  REGISTRATION_CERT: {
    maxSize: 5 * 1024 * 1024, // 5MB
    acceptedTypes: '.pdf,.jpg,.jpeg,.png',
  },
  LIF_LIST: {
    maxSize: 5 * 1024 * 1024, // 5MB per file
    acceptedTypes: '.pdf,.jpg,.jpeg,.png',
  },
  PMA_CERTIFICATE: {
    maxSize: 10 * 1024 * 1024, // 10MB
    acceptedTypes: '.pdf',
  },
} as const;
