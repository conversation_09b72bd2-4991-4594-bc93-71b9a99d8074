import React from 'react';

interface ProjectFormProgressProps {
  currentStep: number;
}

const STEPS = [
  {
    key: 1,
    title: 'Project Details',
    description: 'Basic project information',
  },
  { key: 2, title: 'PMA Certificate', description: 'PMA documentation' },
];

export const ProjectFormProgress = React.memo<ProjectFormProgressProps>(
  ({ currentStep }) => {
    return (
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          {STEPS.map((step, index) => (
            <React.Fragment key={step.key}>
              {/* Step Circle and Label */}
              <div className="flex items-center space-x-4">
                <div
                  className={`flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium transition-all duration-300 ${
                    currentStep >= step.key
                      ? 'bg-primary text-primary-foreground'
                      : 'bg-muted text-muted-foreground'
                  }`}
                >
                  {step.key}
                </div>
                <div className="flex flex-col">
                  <span
                    className={`text-sm font-medium transition-all duration-300 ${
                      currentStep >= step.key
                        ? 'text-foreground'
                        : 'text-muted-foreground'
                    }`}
                  >
                    {step.title}
                  </span>
                  <span className="text-xs text-muted-foreground hidden sm:block">
                    {step.description}
                  </span>
                </div>
              </div>

              {/* Progress Line (not after last step) */}
              {index < STEPS.length - 1 && (
                <div className="flex-1 mx-4 min-w-[40px]">
                  <div className="h-1 bg-muted rounded-full">
                    <div
                      className={`h-1 bg-primary rounded-full transition-all duration-500 ${
                        currentStep > step.key ? 'w-full' : 'w-0'
                      }`}
                    />
                  </div>
                </div>
              )}
            </React.Fragment>
          ))}
        </div>

        {/* Current Step Description for mobile */}
        <div className="sm:hidden text-center">
          <p className="text-xs text-muted-foreground">
            {STEPS[currentStep - 1]?.description}
          </p>
        </div>
      </div>
    );
  },
);

ProjectFormProgress.displayName = 'ProjectFormProgress';
