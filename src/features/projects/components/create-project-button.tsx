'use client';

import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import { useTranslations } from 'next-intl';
import Link from 'next/link';

export function CreateProjectButton() {
  const common = useTranslations('common');

  return (
    <Button size="lg" className="shrink-0" asChild>
      <Link href="/projects/create">
        <Plus className="h-4 w-4 mr-2" />
        {common('new')}
      </Link>
    </Button>
  );
}
