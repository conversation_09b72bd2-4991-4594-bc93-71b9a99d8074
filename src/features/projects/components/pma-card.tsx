'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { FileUploadDropzone } from '@/components/ui/file-upload-dropzone';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Calendar, MapPin, Trash2, Upload } from 'lucide-react';
import { UseFormReturn } from 'react-hook-form';
import { type PmasSchema } from '../schemas/project-schema';

interface PmaCardProps {
  form: UseFormReturn<PmasSchema>;
  index: number;
  onRemove: () => void;
  canRemove: boolean;
}

/**
 * Individual PMA form card
 * Handles the form fields for a single PMA entry
 */
export function PmaCard({ form, index, onRemove, canRemove }: PmaCardProps) {
  return (
    <Card className="relative border-2 border-border/50 hover:border-border transition-colors duration-200">
      <CardHeader className="pb-4 bg-gradient-to-r from-muted/30 to-muted/10 rounded-t-lg">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold flex items-center gap-2">
            <div className="w-2 h-2 bg-primary rounded-full"></div>
            PMA {index + 1}
          </CardTitle>
          {canRemove && (
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={onRemove}
              className="text-destructive hover:text-destructive hover:bg-destructive/10"
            >
              <Trash2 className="h-4 w-4 mr-1" />
              Remove
            </Button>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-6 p-6">
        {/* PMA Number */}
        <div className="space-y-2">
          <Label
            htmlFor={`pma_number_${index}`}
            className="text-sm font-medium flex items-center gap-2"
          >
            <div className="h-4 w-4 text-primary">#</div>
            PMA Number <span className="text-destructive">*</span>
          </Label>
          <Input
            id={`pma_number_${index}`}
            placeholder="Enter PMA number"
            className="text-sm"
            {...form.register(`pmas.${index}.pma_number` as const, {
              onChange: () => {},
              onBlur: (e) => {
                if (!e.target.value.trim()) {
                  form.setError(`pmas.${index}.pma_number`, {
                    type: 'manual',
                    message: 'PMA number is required',
                  });
                }
              },
            })}
          />
          {form.formState.errors.pmas?.[index]?.pma_number && (
            <p className="text-xs text-destructive">
              {form.formState.errors.pmas[index]?.pma_number?.message}
            </p>
          )}
        </div>

        {/* PMA Expiry Date */}
        <div className="space-y-2">
          <Label
            htmlFor={`pma_expiry_${index}`}
            className="text-sm font-medium flex items-center gap-2"
          >
            <Calendar className="h-4 w-4 text-primary" />
            PMA Expiry Date <span className="text-destructive">*</span>
          </Label>
          <Input
            id={`pma_expiry_${index}`}
            type="date"
            className="text-sm"
            {...form.register(`pmas.${index}.expiry_date` as const)}
          />
          {form.formState.errors.pmas?.[index]?.expiry_date && (
            <p className="text-xs text-destructive">
              {form.formState.errors.pmas[index]?.expiry_date?.message}
            </p>
          )}
        </div>

        {/* LIF Location - Full Width */}
        <div className="space-y-2">
          <Label
            htmlFor={`lif_location_${index}`}
            className="text-sm font-medium flex items-center gap-2"
          >
            <MapPin className="h-4 w-4 text-primary" />
            LIF Location <span className="text-destructive">*</span>
          </Label>
          <Textarea
            id={`lif_location_${index}`}
            placeholder="Enter detailed LIF location (e.g., Building A, Level 3, Lift Bank 1)"
            className="min-h-[90px] text-sm resize-none"
            {...form.register(`pmas.${index}.location` as const)}
          />
          {form.formState.errors.pmas?.[index]?.location && (
            <p className="text-xs text-destructive">
              {form.formState.errors.pmas[index]?.location?.message}
            </p>
          )}
        </div>

        {/* File Upload Section */}
        <div className="space-y-2">
          <Label className="text-sm font-medium flex items-center gap-2">
            <Upload className="h-4 w-4 text-primary" />
            PMA Certificate File <span className="text-destructive">*</span>
            <span className="text-xs text-muted-foreground font-normal">
              (PDF only)
            </span>
          </Label>
          <FileUploadDropzone
            onFilesChange={(files) => {
              const file = files[0];
              if (file) {
                form.setValue(`pmas.${index}.file` as const, file);
                form.trigger(`pmas.${index}.file` as const);
              }
            }}
            accept=".pdf"
            maxSize={10 * 1024 * 1024} // 10MB
            maxFiles={1}
            files={(() => {
              const file = form.watch(`pmas.${index}.file` as const);
              return file && file.name ? [file] : [];
            })()}
          />
          {form.formState.errors.pmas?.[index]?.file && (
            <p className="text-xs text-destructive">
              {form.formState.errors.pmas[index]?.file?.message}
            </p>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
