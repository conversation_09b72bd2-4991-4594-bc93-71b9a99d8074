'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { SectionHeader } from '@/components/ui/section-header';
import { zodResolver } from '@hookform/resolvers/zod';
import { ChevronLeft, ChevronRight, Plus } from 'lucide-react';
import { useFieldArray, useForm, UseFormReturn } from 'react-hook-form';
import {
  competentPersonsSchema,
  type CompetentPersonsSchema,
} from '../schemas/project-schema';
import { CompetentPersonCard } from './competent-person-card';

interface CompetentPersonsStepProps {
  form: UseFormReturn<CompetentPersonsSchema>;
  onNext: (data: CompetentPersonsSchema) => void;
  onPrevious: () => void;
  onCancel?: () => void;
  isLoading?: boolean;
}

/**
 * Competent Persons Step - Second step in the multi-step project creation form
 * Handles multiple competent persons information including contact details and file uploads
 */
export function CompetentPersonsStep({
  form,
  onNext,
  onPrevious,
  onCancel,
  isLoading,
}: CompetentPersonsStepProps) {
  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'competent_persons',
  });

  const onSubmit = (data: CompetentPersonsSchema) => {
    console.log('Competent Persons form submission:', data);
    console.log('Form errors:', form.formState.errors);
    console.log('Form is valid:', form.formState.isValid);
    onNext(data);
  };

  const addCompetentPerson = () => {
    append({
      name: '',
      phone_number: '',
      email: '',
      registration_cert_file: undefined,
      lif_list_files: undefined,
    });
  };

  const removeCompetentPerson = (index: number) => {
    remove(index);
  };

  return (
    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-7">
      <SectionHeader
        title="Competent Person Information"
        description="Provide details for the competent persons responsible for this project"
        number={2}
      />

      <div className="space-y-6">
        {/* Competent Persons List */}
        {fields.map((field, index) => (
          <CompetentPersonCard
            key={field.id}
            form={form}
            index={index}
            onRemove={() => removeCompetentPerson(index)}
            canRemove={fields.length > 1}
          />
        ))}

        {/* Array-level validation errors */}
        {form.formState.errors.competent_persons && (
          <p className="text-sm text-destructive">
            {form.formState.errors.competent_persons.message}
          </p>
        )}

        {/* Add Competent Person Button */}
        <Button
          type="button"
          variant="outline"
          onClick={addCompetentPerson}
          className="w-full"
          disabled={fields.length >= 10}
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Another Competent Person
        </Button>
      </div>

      {/* Actions */}
      <div className="flex gap-4 pt-6">
        <Button type="button" variant="outline" onClick={onPrevious}>
          <ChevronLeft className="h-4 w-4 mr-2" />
          Previous
        </Button>
        <Button type="submit" disabled={isLoading} className="min-w-32">
          <ChevronRight className="h-4 w-4 mr-2" />
          Next
        </Button>
        {onCancel && (
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
        )}
      </div>
    </form>
  );
}

/**
 * Hook to create and manage the competent persons form (multiple)
 * @param initialData Optional initial data for the form
 * @returns Form instance and helper functions
 */
export function useCompetentPersonsForm(
  initialData?: Partial<CompetentPersonsSchema>,
) {
  return useForm<CompetentPersonsSchema>({
    resolver: zodResolver(competentPersonsSchema),
    defaultValues: {
      competent_persons: initialData?.competent_persons || [
        {
          name: '',
          phone_number: '',
          email: '',
          registration_cert_file: undefined,
          lif_list_files: undefined,
        },
      ],
    },
  });
}
