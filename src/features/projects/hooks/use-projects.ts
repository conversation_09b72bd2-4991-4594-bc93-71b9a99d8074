import { useUserWithProfile } from '@/hooks/use-auth';
import { toast } from '@/hooks/use-toast';
import { supabase } from '@/lib/supabase';
import type { Database } from '@/types/database';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { createProject } from '../services/project-service';
import type {
  Agency,
  Project,
  ProjectFormData,
  ProjectStats,
  ProjectUserInsert,
  ProjectUserUpdate,
} from '../types/project';

// Type definitions
type User = Database['public']['Tables']['users']['Row'];
type CompetentPerson = User; // Competent persons are users with role 'competent_person'

// Database query helpers for projects

/**
 * Fetch all projects from the database filtered by user's contractor AND projects they're members of
 */
export const useProjects = () => {
  const { data: user } = useUserWithProfile();
  const contractorId = user?.profile?.contractor_id;

  return useQuery({
    queryKey: ['projects', contractorId, user?.id],
    queryFn: async (): Promise<Project[]> => {
      if (!user) return [];

      // Get project IDs where user is a member with accepted status
      const { data: memberProjectIds } = await supabase
        .from('project_users')
        .select('project_id')
        .eq('user_id', user.id)
        .eq('status', 'accepted')
        .eq('is_active', true);

      const memberProjectIdList =
        memberProjectIds?.map((p) => p.project_id) || [];

      // Build the main query
      let query = supabase
        .from('projects')
        .select(
          `
          *,
          agency:agencies(*),
          project_users(
            id,
            role,
            status,
            assigned_date,
            is_active,
            user:users!project_users_user_id_fkey(*)
          )
        `,
        )
        .is('deleted_at', null)
        .order('created_at', { ascending: false });

      // Build access conditions
      const conditions: string[] = [];

      // For Contractor users, include their company's projects
      if (contractorId) {
        conditions.push(`contractor_id.eq.${contractorId}`);
      } else {
        // For JKR users (no contractor_id), show all projects
        conditions.push('id.neq.00000000-0000-0000-0000-000000000000'); // Always true condition
      }

      // Add projects where user is a member
      if (memberProjectIdList.length > 0) {
        conditions.push(`id.in.(${memberProjectIdList.join(',')})`);
      }

      // Apply OR condition to include both owned and member projects
      if (conditions.length > 0) {
        query = query.or(conditions.join(','));
      } else {
        // Fallback: no access
        query = query.eq('id', '00000000-0000-0000-0000-000000000000'); // Never matches
      }

      const { data, error } = await query;

      if (error) {
        throw error;
      }

      return data || [];
    },
    enabled: !!user, // Only run when user data is available
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Fetch project statistics from the database filtered by user's contractor AND projects they're members of
 */
export const useProjectStats = () => {
  const { data: user } = useUserWithProfile();
  const contractorId = user?.profile?.contractor_id;

  return useQuery({
    queryKey: ['projects', 'stats', contractorId, user?.id],
    queryFn: async (): Promise<ProjectStats> => {
      if (!user) return { total: 0, active: 0, pending: 0, completed: 0 };

      // Get project IDs where user is a member with accepted status
      const { data: memberProjectIds } = await supabase
        .from('project_users')
        .select('project_id')
        .eq('user_id', user.id)
        .eq('status', 'accepted')
        .eq('is_active', true);

      const memberProjectIdList =
        memberProjectIds?.map((p) => p.project_id) || [];

      // Build the query
      let query = supabase
        .from('projects')
        .select('status')
        .is('deleted_at', null);

      // Build access conditions
      const conditions: string[] = [];

      // For Contractor users, include their company's projects
      if (contractorId) {
        conditions.push(`contractor_id.eq.${contractorId}`);
      } else {
        // For JKR users (no contractor_id), show all projects
        conditions.push('id.neq.00000000-0000-0000-0000-000000000000'); // Always true condition
      }

      // Add projects where user is a member
      if (memberProjectIdList.length > 0) {
        conditions.push(`id.in.(${memberProjectIdList.join(',')})`);
      }

      // Apply OR condition to include both owned and member projects
      if (conditions.length > 0) {
        query = query.or(conditions.join(','));
      } else {
        // Fallback: no access
        query = query.eq('id', '00000000-0000-0000-0000-000000000000'); // Never matches
      }

      const { data, error } = await query;

      if (error) {
        throw error;
      }

      const projects = data || [];

      const totalProjects = projects.length;
      const activeProjects = projects.filter(
        (p) => p.status === 'active',
      ).length;
      const pendingProjects = projects.filter(
        (p) => p.status === 'pending',
      ).length;
      const completedProjects = projects.filter(
        (p) => p.status === 'completed',
      ).length;

      return {
        total: totalProjects,
        active: activeProjects,
        pending: pendingProjects,
        completed: completedProjects,
      };
    },
    enabled: !!user, // Only run when user data is available
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Create a new project
 */
export const useCreateProject = () => {
  const queryClient = useQueryClient();
  const { data: user } = useUserWithProfile();

  return useMutation({
    mutationFn: async (data: ProjectFormData): Promise<Project> => {
      // Get current user session
      const { data: session } = await supabase.auth.getSession();
      if (!session.session?.user) {
        throw new Error('User not authenticated');
      }

      const userId = session.session.user.id;

      // Create project with all related data
      const result = await createProject(data, userId);

      return result.project;
    },
    onSuccess: (data) => {
      const contractorId = user?.profile?.contractor_id;

      // Invalidate and refetch projects data with the correct query keys
      queryClient.invalidateQueries({
        queryKey: ['projects', contractorId, user?.id],
      });
      queryClient.invalidateQueries({
        queryKey: ['projects', 'stats', contractorId, user?.id],
      });

      toast({
        title: 'Project Created Successfully',
        description: `Project "${data.name}" has been created and is now pending review.`,
      });
    },
    onError: (error) => {
      toast({
        title: 'Failed to Create Project',
        description:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred while creating the project.',
        variant: 'destructive',
      });
    },
  });
};

/**
 * Fetch a single project by ID (with contractor filtering and member access for access control)
 */
export const useProject = (projectId: string) => {
  const { data: user } = useUserWithProfile();
  const contractorId = user?.profile?.contractor_id;

  return useQuery({
    queryKey: ['projects', projectId, contractorId, user?.id],
    queryFn: async (): Promise<Project | null> => {
      if (!user || !projectId) return null;

      // Check if user is a member of this project
      const { data: memberCheck } = await supabase
        .from('project_users')
        .select('id')
        .eq('project_id', projectId)
        .eq('user_id', user.id)
        .eq('is_active', true)
        .maybeSingle();

      // Build query with access control
      let query = supabase
        .from('projects')
        .select(
          `
          *,
          agency:agencies(*),
          project_users(
            id,
            role,
            status,
            assigned_date,
            is_active,
            user:users!project_users_user_id_fkey(*)
          )
        `,
        )
        .eq('id', projectId)
        .is('deleted_at', null);

      // Build access conditions
      const conditions: string[] = [];

      // For Contractor users, include their company's projects
      if (contractorId) {
        conditions.push(`contractor_id.eq.${contractorId}`);
      } else {
        // For JKR users (no contractor_id), allow all projects
        conditions.push('id.neq.00000000-0000-0000-0000-000000000000'); // Always true
      }

      // If user is a member, allow access regardless of ownership
      if (memberCheck) {
        conditions.push(`id.eq.${projectId}`);
      }

      // Apply OR condition to include both owned and member projects
      if (conditions.length > 0) {
        query = query.or(conditions.join(','));
      } else {
        // Fallback: no access
        query = query.eq('id', '00000000-0000-0000-0000-000000000000'); // Never matches
      }

      const { data, error } = await query.single();

      if (error) {
        if (error.code === 'PGRST116') {
          // No rows returned - could be project doesn't exist or user doesn't have access
          return null;
        }
        throw error;
      }

      return data;
    },
    enabled: !!projectId && !!user,
  });
};

/**
 * Fetch all competent persons for dropdown selection
 */
export const useCompetentPersons = () => {
  return useQuery({
    queryKey: ['competent-persons'],
    queryFn: async (): Promise<CompetentPerson[]> => {
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .is('deleted_at', null)
        .order('name', { ascending: true });

      if (error) throw error;
      return data || [];
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Fetch all agencies for dropdown selection
 */
export const useAgencies = () => {
  return useQuery({
    queryKey: ['agencies'],
    queryFn: async (): Promise<Agency[]> => {
      const { data, error } = await supabase
        .from('agencies')
        .select('*')
        .is('deleted_at', null)
        .order('name');

      if (error) {
        throw error;
      }

      return data || [];
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

/**
 * Fetch all JKR users for dropdown selection
 * @param state Optional state filter to fetch JKR users for a specific state
 */
export const useJkrUsers = (state?: string) => {
  return useQuery({
    queryKey: ['jkr-users', state],
    queryFn: async (): Promise<
      Database['public']['Tables']['users']['Row'][]
    > => {
      let query = supabase
        .from('users')
        .select('*')
        .eq('user_role', 'admin')
        .is('deleted_at', null);

      // If state is provided, filter admin users based on their access mode:
      // - Project-level admins (admin_access_mode = 'project') can access all states
      // - State-level admins (admin_access_mode = 'state') can only access their monitoring_state
      if (state) {
        query = query.or(
          `admin_access_mode.eq.project,and(admin_access_mode.eq.state,monitoring_state.eq.${state})`,
        );
      }

      const { data, error } = await query.order('name');

      if (error) {
        throw error;
      }

      return data || [];
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

/**
 * Hook to manage project-user assignments
 */
export const useProjectUsers = () => {
  const queryClient = useQueryClient();

  const assignUserToProject = useMutation({
    mutationFn: async (data: ProjectUserInsert) => {
      const { data: result, error } = await supabase
        .from('project_users')
        .insert(data)
        .select()
        .single();

      if (error) throw error;
      return result;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['projects'] });
    },
  });

  const removeUserFromProject = useMutation({
    mutationFn: async ({
      projectId,
      userId,
    }: {
      projectId: string;
      userId: string;
    }) => {
      const { error } = await supabase
        .from('project_users')
        .delete()
        .eq('project_id', projectId)
        .eq('user_id', userId);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['projects'] });
    },
  });

  const updateProjectUser = useMutation({
    mutationFn: async ({
      id,
      updates,
    }: {
      id: string;
      updates: ProjectUserUpdate;
    }) => {
      const { data: result, error } = await supabase
        .from('project_users')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return result;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['projects'] });
    },
  });

  return {
    assignUserToProject,
    removeUserFromProject,
    updateProjectUser,
  };
};
