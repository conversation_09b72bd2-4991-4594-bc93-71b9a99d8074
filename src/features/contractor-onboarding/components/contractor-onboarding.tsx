'use client';

import { useContractorOnboarding } from '../hooks/use-contractor-onboarding';
import type { ContractorFullFormValues } from '../schemas/contractor-onboarding-schemas';
import { ContractorOnboardingProgress } from './contractor-onboarding-progress';
import { ContractorOnboardingStep1 } from './contractor-onboarding-step1';
import { ContractorOnboardingStep3 } from './contractor-onboarding-step3';
import { ContractorOnboardingStep4 } from './contractor-onboarding-step4';

interface ContractorOnboardingProps {
  onSubmit?: (values: ContractorFullFormValues) => void | Promise<void>;
  onSkipToCompletion?: () => void | Promise<void>;
  className?: string;
}

export function ContractorOnboarding({
  onSubmit,
  onSkipToCompletion,
  className,
}: ContractorOnboardingProps) {
  const {
    currentStep,
    step2Data,
    step1Form,
    step2Form,
    step3Form,
    isCodeCopied,
    companyRegistrationType,
    watchedCompanyType,
    handleStep1Submit,
    handleStep2Submit,
    handleStep3Submit,
    handleCopyCode,
    regenerateCode,
    goToPreviousStep,
  } = useContractorOnboarding({ onSubmit, onSkipToCompletion });

  return (
    <div
      className={`bg-card rounded-3xl shadow-lg p-8 lg:p-12 border ${className || ''}`}
    >
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-foreground mb-2">
          Contractor Registration
        </h1>
        <p className="text-muted-foreground">
          Complete your contractor profile information and company setup
        </p>
      </div>
      {/* Progress Indicator */}
      <ContractorOnboardingProgress
        currentStep={currentStep}
        step2Data={step2Data}
      />
      {/* Current Step Content */}
      {currentStep === 1 && (
        <ContractorOnboardingStep1
          form={step1Form}
          onSubmit={handleStep1Submit}
        />
      )}
      {currentStep === 2 && (
        <ContractorOnboardingStep3
          form={step2Form}
          onSubmit={handleStep2Submit}
          onPrevious={goToPreviousStep}
          companyRegistrationType={companyRegistrationType}
        />
      )}
      {currentStep === 3 && (
        <ContractorOnboardingStep4
          form={step3Form}
          onSubmit={handleStep3Submit}
          onPrevious={goToPreviousStep}
          watchedCompanyType={watchedCompanyType}
          isCodeCopied={isCodeCopied}
          onCopyCode={handleCopyCode}
          onRegenerateCode={regenerateCode}
        />
      )}
      {/* Footer */}
      <div className="mt-8 pt-6 border-t border-border">
        <p className="text-sm text-muted-foreground text-center">
          All fields marked with <span className="text-destructive">*</span> are
          required
        </p>
      </div>
    </div>
  );
}
