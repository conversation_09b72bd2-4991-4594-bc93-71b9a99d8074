import React from 'react';
import { type ContractorStep2FormValues } from '../schemas/contractor-onboarding-schemas';

interface ContractorOnboardingProgressProps {
  currentStep: number;
  step2Data: ContractorStep2FormValues | null;
}

export const ContractorOnboardingProgress =
  React.memo<ContractorOnboardingProgressProps>(
    ({ currentStep, step2Data }) => {
      const showStep3 =
        step2Data?.companyRegistrationType === 'create' || currentStep === 3;

      return (
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            {/* Step 1: Personal Information */}
            <div className="flex items-center space-x-4">
              <div
                className={`flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium ${
                  currentStep >= 1
                    ? 'bg-primary text-primary-foreground'
                    : 'bg-muted text-muted-foreground'
                }`}
              >
                1
              </div>
              <span
                className={`text-sm font-medium ${
                  currentStep >= 1 ? 'text-foreground' : 'text-muted-foreground'
                }`}
              >
                Personal Information
              </span>
            </div>

            <div className="flex-1 mx-4">
              <div className="h-1 bg-muted rounded-full">
                <div
                  className={`h-1 bg-primary rounded-full transition-all duration-300 ${
                    currentStep >= 2 ? 'w-full' : 'w-0'
                  }`}
                />
              </div>
            </div>

            {/* Step 2: Company Setup */}
            <div className="flex items-center space-x-4">
              <div
                className={`flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium ${
                  currentStep >= 2
                    ? 'bg-primary text-primary-foreground'
                    : 'bg-muted text-muted-foreground'
                }`}
              >
                2
              </div>
              <span
                className={`text-sm font-medium ${
                  currentStep >= 2 ? 'text-foreground' : 'text-muted-foreground'
                }`}
              >
                Company Setup
              </span>
            </div>

            {/* Only show step 3 if creating a company */}
            {showStep3 && (
              <>
                <div className="flex-1 mx-4">
                  <div className="h-1 bg-muted rounded-full">
                    <div
                      className={`h-1 bg-primary rounded-full transition-all duration-300 ${
                        currentStep >= 3 ? 'w-full' : 'w-0'
                      }`}
                    />
                  </div>
                </div>

                <div className="flex items-center space-x-4">
                  <div
                    className={`flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium ${
                      currentStep >= 3
                        ? 'bg-primary text-primary-foreground'
                        : 'bg-muted text-muted-foreground'
                    }`}
                  >
                    3
                  </div>
                  <span
                    className={`text-sm font-medium ${
                      currentStep >= 3
                        ? 'text-foreground'
                        : 'text-muted-foreground'
                    }`}
                  >
                    Company Creation
                  </span>
                </div>
              </>
            )}
          </div>
        </div>
      );
    },
  );

ContractorOnboardingProgress.displayName = 'ContractorOnboardingProgress';
