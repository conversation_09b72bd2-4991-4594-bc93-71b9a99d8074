'use client';

import * as React from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

export function CreateCompanyForm() {
  const handleSubmit = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    console.log('Create company form submitted');
  };

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8 lg:py-12">
        <div className="w-full max-w-3xl mx-auto">
          <form
            className="bg-card rounded-3xl shadow-lg p-8 lg:p-12 border"
            onSubmit={handleSubmit}
          >
            {/* Header */}
            <div className="mb-8">
              <h1 className="text-2xl font-bold text-foreground mb-2">
                Create New Company
              </h1>
              <p className="text-muted-foreground">
                Register your company to start collaborating on projects
              </p>
            </div>

            {/* Form Fields */}
            <div className="space-y-6">
              <div>
                <Label
                  htmlFor="company-name"
                  className="text-sm font-medium text-foreground"
                >
                  Company Name <span className="text-destructive">*</span>
                </Label>
                <Input
                  type="text"
                  id="company-name"
                  name="company-name"
                  placeholder="Enter your company name"
                  className="mt-2"
                  required
                />
              </div>

              <div>
                <Label
                  htmlFor="company-registration"
                  className="text-sm font-medium text-foreground"
                >
                  Company Registration Number{' '}
                  <span className="text-destructive">*</span>
                </Label>
                <Input
                  type="text"
                  id="company-registration"
                  name="company-registration"
                  placeholder="e.g., 123456-A"
                  className="mt-2"
                  required
                />
              </div>

              <div>
                <Label
                  htmlFor="company-type"
                  className="text-sm font-medium text-foreground"
                >
                  Company Type <span className="text-destructive">*</span>
                </Label>
                <Select name="company-type">
                  <SelectTrigger id="company-type" className="mt-2 w-full">
                    <SelectValue placeholder="Select company type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="contractor">Contractor</SelectItem>
                    <SelectItem value="consultant">Consultant</SelectItem>
                    <SelectItem value="supplier">Supplier</SelectItem>
                    <SelectItem value="government">
                      Government Agency
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label
                  htmlFor="company-address"
                  className="text-sm font-medium text-foreground"
                >
                  Company Address <span className="text-destructive">*</span>
                </Label>
                <Textarea
                  id="company-address"
                  name="company-address"
                  placeholder="Enter your company address"
                  className="mt-2"
                  required
                />
              </div>

              <div>
                <Label
                  htmlFor="company-phone"
                  className="text-sm font-medium text-foreground"
                >
                  Company Phone <span className="text-destructive">*</span>
                </Label>
                <Input
                  type="tel"
                  id="company-phone"
                  name="company-phone"
                  placeholder="+60 3-1234 5678"
                  className="mt-2"
                  required
                />
              </div>

              <div>
                <Label
                  htmlFor="company-email"
                  className="text-sm font-medium text-foreground"
                >
                  Company Email <span className="text-destructive">*</span>
                </Label>
                <Input
                  type="email"
                  id="company-email"
                  name="company-email"
                  placeholder="<EMAIL>"
                  className="mt-2"
                  required
                />
              </div>
            </div>

            {/* Submit Button */}
            <div className="flex flex-col sm:flex-row items-center justify-between pt-8 mt-8 border-t border-border gap-4">
              <p className="text-sm text-muted-foreground">
                All fields marked with{' '}
                <span className="text-destructive">*</span> are required
              </p>
              <Button type="submit" className="w-full sm:w-auto px-8 py-3">
                Create Company
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
