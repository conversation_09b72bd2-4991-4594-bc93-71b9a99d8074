'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { SectionHeader } from '@/components/ui/section-header';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { generateCompanyCode } from '@/lib/utils';
import { zodResolver } from '@hookform/resolvers/zod';
import { Check, Copy, RefreshCw } from 'lucide-react';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import {
  companyFormSchema,
  type CompanyFormValues,
} from '../schemas/contractor-onboarding-schemas';

interface CompanyFormProps {
  onSubmit?: (values: CompanyFormValues) => void;
  onCancel?: () => void;
}

export function CompanyForm({ onSubmit, onCancel }: CompanyFormProps) {
  const [isCodeCopied, setIsCodeCopied] = useState<boolean>(false);

  // Initialize form with react-hook-form and Zod validation
  const form = useForm<CompanyFormValues>({
    resolver: zodResolver(companyFormSchema),
    defaultValues: {
      company_name: '',
      company_type: undefined,
      company_hotline: '',
      oem_name: '',
      code: generateCompanyCode(), // Auto-generate code on initialization
    },
  });

  const watchedCompanyType = form.watch('company_type');

  // Generate new company code
  const regenerateCode = () => {
    const newCode = generateCompanyCode();
    form.setValue('code', newCode);
  };

  // Copy code to clipboard
  const handleCopyCode = async () => {
    try {
      const code = form.getValues('code');
      await navigator.clipboard.writeText(code);
      setIsCodeCopied(true);
      setTimeout(() => setIsCodeCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy code:', err);
    }
  };

  // Handle form submission
  const handleSubmit = (values: CompanyFormValues) => {
    if (onSubmit) {
      onSubmit(values);
    }
  };

  // Handle cancel
  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    }
  };

  return (
    <Form {...form}>
      <form
        className="bg-card rounded-3xl shadow-lg p-8 lg:p-12 border"
        onSubmit={form.handleSubmit(handleSubmit)}
      >
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-2xl font-bold text-foreground mb-2">
            Company Registration
          </h1>
          <p className="text-muted-foreground">
            Register your company in the contractor database
          </p>
        </div>

        {/* Form Sections */}
        <div className="grid grid-cols-1 xl:grid-cols-2 gap-8 lg:gap-12">
          {/* Company Information Section */}
          <div className="xl:col-span-1">
            <SectionHeader number={1} title="Company Information" />

            <div className="space-y-7">
              {/* Auto-generated Company Code */}
              <FormField
                control={form.control}
                name="code"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Company Code</FormLabel>
                    <div className="flex gap-2">
                      <FormControl>
                        <Input
                          {...field}
                          readOnly
                          className="font-mono text-center tracking-wider bg-muted text-sm"
                          placeholder="Generating..."
                        />
                      </FormControl>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={handleCopyCode}
                        className="shrink-0"
                      >
                        {isCodeCopied ? (
                          <Check className="w-4 h-4" />
                        ) : (
                          <Copy className="w-4 h-4" />
                        )}
                      </Button>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={regenerateCode}
                        className="shrink-0"
                      >
                        <RefreshCw className="w-4 h-4" />
                      </Button>
                    </div>
                    <FormDescription>
                      This unique code will identify your company in the system.
                      Format: YYMM-XXXX-XXXX
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="company_name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Company Name <span className="text-destructive">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input placeholder="Enter your company name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="company_type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Company Type <span className="text-destructive">*</span>
                    </FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select your company type" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="COMPETENT_FIRM">
                          Competent Firm
                        </SelectItem>
                        <SelectItem value="NON_COMPETENT_FIRM">
                          Non-Competent Firm
                        </SelectItem>
                        <SelectItem value="OEM">
                          OEM (Original Equipment Manufacturer)
                        </SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="company_hotline"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Company Hotline{' '}
                      <span className="text-destructive">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input
                        type="tel"
                        placeholder="Enter company hotline number"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>

          {/* OEM Details Section - Only show if OEM is selected */}
          {watchedCompanyType === 'OEM' && (
            <div className="xl:col-span-1">
              <SectionHeader number={2} title="OEM Details" />

              <div className="space-y-7">
                <FormField
                  control={form.control}
                  name="oem_name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        OEM Name <span className="text-destructive">*</span>
                      </FormLabel>
                      <FormControl>
                        <Input placeholder="Enter OEM name" {...field} />
                      </FormControl>
                      <FormDescription>
                        Required for OEM type companies
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
          )}

          {/* Appointed OEM/Competent Firm Section - Only show if NON_COMPETENT_FIRM is selected */}
          {watchedCompanyType === 'NON_COMPETENT_FIRM' && (
            <div className="xl:col-span-1">
              <SectionHeader number={2} title="Appointed Firm Details" />

              <div className="space-y-7">
                <FormField
                  control={form.control}
                  name="appointed_oem_competent_firm"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Appointed OEM/Competent Firm{' '}
                        <span className="text-destructive">*</span>
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter appointed OEM or competent firm name"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Required for non-competent firms - specify the OEM or
                        competent firm appointed for your services
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
          )}
        </div>

        {/* Submit Button */}
        <div className="flex flex-col sm:flex-row items-center justify-between pt-8 mt-8 border-t border-border gap-4">
          <p className="text-sm text-muted-foreground">
            All fields marked with <span className="text-destructive">*</span>{' '}
            are required
          </p>
          <div className="flex gap-3">
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              className="px-8 py-3"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="px-8 py-3"
              disabled={form.formState.isSubmitting}
            >
              {form.formState.isSubmitting
                ? 'Registering...'
                : 'Register Company'}
            </Button>
          </div>
        </div>
      </form>
    </Form>
  );
}
