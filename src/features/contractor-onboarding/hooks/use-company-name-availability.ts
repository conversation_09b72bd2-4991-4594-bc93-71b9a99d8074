import { supabase } from '@/lib/supabase';
import { useQuery } from '@tanstack/react-query';

export function useCheckCompanyNameAvailability(companyName: string) {
  return useQuery({
    queryKey: ['company-name-availability', companyName],
    queryFn: async () => {
      if (!companyName || companyName.trim().length < 2) {
        return { available: null, message: '' };
      }

      const uppercaseCompanyName = companyName.trim().toUpperCase();

      const { data, error } = await supabase
        .from('contractors')
        .select('id')
        .eq('name', uppercaseCompanyName)
        .maybeSingle();

      if (error) {
        console.error('Error checking company name availability:', error);
        return { available: null, message: 'Unable to check availability' };
      }

      const available = !data;
      return {
        available,
        message: available
          ? `"${uppercaseCompanyName}" is available`
          : `"${uppercaseCompanyName}" is already taken`,
      };
    },
    enabled: Boolean(companyName && companyName.trim().length >= 2),
    staleTime: 30000, // 30 seconds
    retry: false,
  });
}
