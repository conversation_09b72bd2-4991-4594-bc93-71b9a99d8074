export interface PmaFormData {
  customer: string;
  agency: string;
  location: string;
  pmaNumber: string;
  type: string;
  supervisor: string;
  dateReceived: string;
  assignCp: string;
}

export interface PmaEntry extends PmaFormData {
  id: string;
  status: 'ready' | 'draft';
  createdAt: string;
}

export interface PmaFormState {
  entries: PmaEntry[];
  currentForm: Partial<PmaFormData>;
}
