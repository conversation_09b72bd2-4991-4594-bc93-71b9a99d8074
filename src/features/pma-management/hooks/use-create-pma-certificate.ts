import { useMutation, useQueryClient } from '@tanstack/react-query';
import { createPMACertificate } from '../services/create-pma-certificate';
import type {
  CreatePMACertificateInput,
  CreatePMACertificateResponse,
} from '../types/create-pma';
import { PMA_CERTIFICATES_QUERY_KEY } from './use-pma-certificates';

export function useCreatePMACertificate() {
  const queryClient = useQueryClient();

  return useMutation<
    CreatePMACertificateResponse,
    Error,
    CreatePMACertificateInput
  >({
    mutationFn: async (data) => {
      // Debug: Log the input data
      console.log('Creating PMA certificate, input data:', {
        ...data,
        file_url: data.file_url ? '[FILE URL]' : null, // Mask the full URL
      });

      try {
        const result = await createPMACertificate(data);
        console.log('PMA certificate created successfully:', {
          id: result.id,
          pma_number: result.pma_number,
          project_id: result.project_id,
        });
        return result;
      } catch (error) {
        console.error('Failed to create PMA certificate:', error);
        throw error;
      }
    },
    onSuccess: (data) => {
      // Invalidate any existing queries for PMA certificates for this project
      queryClient.invalidateQueries({
        queryKey: [PMA_CERTIFICATES_QUERY_KEY, data.project_id],
      });
    },
    // Add mutation key to fix TanStack Query error
    mutationKey: ['createPMACertificate'],
  });
}
