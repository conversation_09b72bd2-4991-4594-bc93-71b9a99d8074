import { useQuery } from '@tanstack/react-query';
import { fetchPMACertificates } from '../services/pma-certificate-service';
import type { PMACertificate } from '../types/pma-certificate';

export const PMA_CERTIFICATES_QUERY_KEY = 'pma-certificates';

export function usePMACertificates(projectId: string | null) {
  return useQuery<PMACertificate[], Error>({
    queryKey: ['pma-certificates', projectId],
    queryFn: () => fetchPMACertificates(projectId!),
    enabled: !!projectId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}
