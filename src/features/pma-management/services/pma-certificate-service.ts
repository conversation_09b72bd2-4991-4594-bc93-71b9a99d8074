import { supabase } from '@/lib/supabase';
import type { PMACertificate } from '../types/pma-certificate';

export async function fetchPMACertificates(
  projectId: string,
): Promise<PMACertificate[]> {
  if (!projectId) {
    throw new Error('Project ID is required');
  }

  const { data, error } = await supabase
    .from('pma_certificates')
    .select(
      `
            id,
            pma_number,
            expiry_date,
            status,
            project_id,
            competent_person_id,
            location,
            state,
            created_at,
            updated_at
        `,
    )
    .eq('project_id', projectId)
    .order('expiry_date', { ascending: false });

  if (error) {
    console.error('Failed to fetch PMA certificates:', error);
    throw new Error(`Failed to fetch PMA certificates: ${error.message}`);
  }

  // Filter out certificates with null expiry_date (but allow null pma_number) and transform to proper type
  const filteredData = (data || [])
    .filter((cert) => cert.expiry_date !== null)
    .map((cert) => ({
      id: cert.id,
      pma_number: cert.pma_number || `Draft-${cert.id.slice(0, 8)}`, // Use fallback for null pma_number
      expiry_date: cert.expiry_date,
      status: cert.status,
      project_id: cert.project_id!,
      competent_person_id: cert.competent_person_id,
      location: cert.location,
      state: cert.state,
      created_at: cert.created_at,
      updated_at: cert.updated_at,
    }));

  console.log(
    `Found ${filteredData.length} PMA certificate(s) for project ${projectId}`,
  );

  return filteredData;
}
