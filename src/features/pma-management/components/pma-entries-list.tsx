'use client';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { usePmaManagementTranslations } from '@/hooks/use-translations';
import { Edit2, FileText, Plus, Trash2 } from 'lucide-react';
import { type PmaEntry } from '../types/pma-types';

interface PmaEntriesListProps {
  entries: PmaEntry[];
  onEdit: (entry: PmaEntry) => void;
  onDelete: (id: string) => void;
  onAddMore: () => void;
}

export function PmaEntriesList({
  entries,
  onEdit,
  onDelete,
  onAddMore,
}: PmaEntriesListProps) {
  const t = usePmaManagementTranslations();

  if (entries.length === 0) {
    return null;
  }

  return (
    <Card>
      <CardHeader className="pb-4">
        <div className="flex items-center gap-2">
          <FileText className="h-5 w-5 text-primary" />
          <div>
            <CardTitle className="text-lg">
              {t('form.addedPmaEntries')}
            </CardTitle>
            <p className="text-sm text-muted-foreground mt-1">
              {t('form.addedPmaEntriesDescription')}
            </p>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {entries.map((entry) => (
          <div
            key={entry.id}
            className="border rounded-lg p-4 bg-gradient-to-r from-blue-50/50 to-blue-50/30 hover:from-blue-50 hover:to-blue-50/50 transition-colors"
          >
            <div className="flex items-start justify-between">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <FileText className="h-4 w-4 text-blue-600" />
                </div>
                <div>
                  <div className="font-medium text-lg">{entry.pmaNumber}</div>
                  <div className="text-sm text-muted-foreground">
                    {entry.location} • {entry.supervisor}
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Badge
                  variant={entry.status === 'ready' ? 'default' : 'secondary'}
                  className={
                    entry.status === 'ready'
                      ? 'bg-green-100 text-green-700 hover:bg-green-100'
                      : ''
                  }
                >
                  {entry.status === 'ready' ? t('form.ready') : 'Draft'}
                </Badge>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onEdit(entry)}
                  className="h-8 w-8 p-0"
                >
                  <Edit2 className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onDelete(entry.id)}
                  className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </div>

            {/* Entry Details */}
            <div className="mt-3 grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <span className="text-muted-foreground">Customer:</span>
                <div className="font-medium">{entry.customer}</div>
              </div>
              <div>
                <span className="text-muted-foreground">Agency:</span>
                <div className="font-medium">{entry.agency}</div>
              </div>
              <div>
                <span className="text-muted-foreground">Type:</span>
                <div className="font-medium">{entry.type}</div>
              </div>
              <div>
                <span className="text-muted-foreground">Date:</span>
                <div className="font-medium">{entry.dateReceived}</div>
              </div>
            </div>
          </div>
        ))}

        {/* Add More Button */}
        <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-8">
          <div className="text-center">
            <Button
              variant="ghost"
              onClick={onAddMore}
              className="flex flex-col items-center gap-2 h-auto py-4 text-muted-foreground hover:text-foreground"
            >
              <Plus className="h-6 w-6" />
              <span className="text-sm">{t('addMorePmaEntries')}</span>
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
