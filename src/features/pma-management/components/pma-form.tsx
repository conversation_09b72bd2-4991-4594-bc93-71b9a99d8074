'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { usePmaManagementTranslations } from '@/hooks/use-translations';
import { zodResolver } from '@hookform/resolvers/zod';
import { FileText } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { pmaFormSchema, type PmaFormSchema } from '../schemas/pma-schema';
import { type PmaFormData } from '../types/pma-types';

interface PmaFormProps {
  onSubmit: (data: PmaFormData) => void;
  initialData?: Partial<PmaFormData>;
}

export function PmaForm({ onSubmit, initialData }: PmaFormProps) {
  const t = usePmaManagementTranslations();

  const form = useForm<PmaFormSchema>({
    resolver: zodResolver(pmaFormSchema),
    defaultValues: {
      customer: initialData?.customer || '',
      agency: initialData?.agency || '',
      location: initialData?.location || '',
      pmaNumber: initialData?.pmaNumber || '',
      type: initialData?.type || '',
      supervisor: initialData?.supervisor || '',
      dateReceived: initialData?.dateReceived || '',
      assignCp: initialData?.assignCp || '',
    },
  });

  const handleSubmit = (data: PmaFormSchema) => {
    onSubmit(data);
    form.reset();
  };

  return (
    <Card>
      <CardHeader className="pb-4">
        <div className="flex items-center gap-2">
          <FileText className="h-5 w-5 text-primary" />
          <div>
            <CardTitle className="text-lg">
              {t('form.pmaInformation')}
            </CardTitle>
            <p className="text-sm text-muted-foreground mt-1">
              {t('form.pmaInformationDescription')}
            </p>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
          {/* First Row */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Customer */}
            <div className="space-y-2">
              <Label htmlFor="customer" className="text-sm font-medium">
                {t('form.customer')}
              </Label>
              <Input
                id="customer"
                placeholder={t('form.customerPlaceholder')}
                {...form.register('customer')}
                className={
                  form.formState.errors.customer ? 'border-red-500' : ''
                }
              />
              {form.formState.errors.customer && (
                <p className="text-xs text-red-500">
                  {form.formState.errors.customer.message}
                </p>
              )}
            </div>

            {/* Agency */}
            <div className="space-y-2">
              <Label htmlFor="agency" className="text-sm font-medium">
                {t('form.agency')}
              </Label>
              <Input
                id="agency"
                placeholder={t('form.agencyPlaceholder')}
                {...form.register('agency')}
                className={form.formState.errors.agency ? 'border-red-500' : ''}
              />
              {form.formState.errors.agency && (
                <p className="text-xs text-red-500">
                  {form.formState.errors.agency.message}
                </p>
              )}
            </div>
          </div>

          {/* Second Row */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Location */}
            <div className="space-y-2">
              <Label htmlFor="location" className="text-sm font-medium">
                {t('form.location')}
              </Label>
              <Textarea
                id="location"
                placeholder={t('form.locationPlaceholder')}
                {...form.register('location')}
                className={`min-h-[80px] resize-none ${form.formState.errors.location ? 'border-red-500' : ''}`}
              />
              {form.formState.errors.location && (
                <p className="text-xs text-red-500">
                  {form.formState.errors.location.message}
                </p>
              )}
            </div>

            {/* PMA Number */}
            <div className="space-y-2">
              <Label htmlFor="pmaNumber" className="text-sm font-medium">
                {t('form.pmaNumber')}
              </Label>
              <Input
                id="pmaNumber"
                placeholder={t('form.pmaNumberPlaceholder')}
                {...form.register('pmaNumber')}
                className={
                  form.formState.errors.pmaNumber ? 'border-red-500' : ''
                }
              />
              {form.formState.errors.pmaNumber && (
                <p className="text-xs text-red-500">
                  {form.formState.errors.pmaNumber.message}
                </p>
              )}
            </div>
          </div>

          {/* Third Row */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Type */}
            <div className="space-y-2">
              <Label htmlFor="type" className="text-sm font-medium">
                {t('form.type')}
              </Label>
              <Input
                id="type"
                placeholder="Enter brand/type"
                {...form.register('type')}
                className={form.formState.errors.type ? 'border-red-500' : ''}
              />
              {form.formState.errors.type && (
                <p className="text-xs text-red-500">
                  {form.formState.errors.type.message}
                </p>
              )}
            </div>

            {/* Supervisor */}
            <div className="space-y-2">
              <Label htmlFor="supervisor" className="text-sm font-medium">
                {t('form.supervisor')}
              </Label>
              <Input
                id="supervisor"
                placeholder={t('form.supervisorPlaceholder')}
                {...form.register('supervisor')}
                className={
                  form.formState.errors.supervisor ? 'border-red-500' : ''
                }
              />
              {form.formState.errors.supervisor && (
                <p className="text-xs text-red-500">
                  {form.formState.errors.supervisor.message}
                </p>
              )}
            </div>

            {/* Date Received */}
            <div className="space-y-2">
              <Label htmlFor="dateReceived" className="text-sm font-medium">
                {t('form.dateReceived')}
              </Label>
              <Input
                id="dateReceived"
                type="date"
                {...form.register('dateReceived')}
                className={
                  form.formState.errors.dateReceived ? 'border-red-500' : ''
                }
              />
              {form.formState.errors.dateReceived && (
                <p className="text-xs text-red-500">
                  {form.formState.errors.dateReceived.message}
                </p>
              )}
            </div>
          </div>

          {/* Fourth Row */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Assign CP */}
            <div className="space-y-2">
              <Label htmlFor="assignCp" className="text-sm font-medium">
                {t('form.assignCp')}
              </Label>
              <Input
                id="assignCp"
                placeholder="Select competent person"
                {...form.register('assignCp')}
                className={
                  form.formState.errors.assignCp ? 'border-red-500' : ''
                }
              />
              {form.formState.errors.assignCp && (
                <p className="text-xs text-red-500">
                  {form.formState.errors.assignCp.message}
                </p>
              )}
            </div>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end pt-4">
            <Button type="submit" className="px-6">
              Add PMA Entry
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
