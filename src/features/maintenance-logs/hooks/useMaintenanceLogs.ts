import { useProjectContext } from '@/providers/project-context';
import { useQuery } from '@tanstack/react-query';
import { fetchMaintenanceLogs } from '../services/maintenanceLogsService';
import type { MaintenanceLogsFilters } from '../types/table';

interface UseMaintenanceLogsParams {
  filters: MaintenanceLogsFilters;
  pageIndex: number;
  pageSize: number;
  sorting?: {
    column?: string;
    direction?: 'asc' | 'desc';
  };
}

export function useMaintenanceLogs(params: UseMaintenanceLogsParams) {
  const { selectedProjectId } = useProjectContext();

  return useQuery({
    queryKey: [
      'maintenance-logs',
      selectedProjectId,
      params.filters,
      params.pageIndex,
      params.pageSize,
      params.sorting,
    ],
    queryFn: () => {
      if (!selectedProjectId) {
        throw new Error('No project selected');
      }

      return fetchMaintenanceLogs({
        projectId: selectedProjectId,
        filters: params.filters,
        pageIndex: params.pageIndex,
        pageSize: params.pageSize,
        sorting: params.sorting,
      });
    },
    enabled: !!selectedProjectId,
    staleTime: 30 * 1000, // 30 seconds
    gcTime: 5 * 60 * 1000, // 5 minutes
  });
}
