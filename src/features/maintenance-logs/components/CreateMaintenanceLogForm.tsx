'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { MaintenanceTypeBadge } from '@/features/maintenance-logs/components/MaintenanceTypeBadge';
import type { PMACertificate } from '@/features/pma-management';
import { cn } from '@/lib/utils';
import { zodResolver } from '@hookform/resolvers/zod';
import { format } from 'date-fns';
import { AlertCircle } from 'lucide-react';
import { useForm } from 'react-hook-form';
import {
  createMaintenanceLogSchema,
  OPERATION_LOG_TYPES,
  type CreateMaintenanceLogInput,
} from '../schemas/create-maintenance-log';

interface CreateMaintenanceLogFormProps {
  onSubmit: (data: CreateMaintenanceLogInput) => Promise<void>;
  projectData: {
    contractor_id: string;
    contractor_name: string;
    competent_person_name: string;
    competent_person_phone: string;
  } | null;
  pmaCertificates: PMACertificate[];
  isSubmitting?: boolean;
}

export default function CreateMaintenanceLogForm({
  onSubmit,
  projectData,
  pmaCertificates = [],
  isSubmitting = false,
}: CreateMaintenanceLogFormProps) {
  const form = useForm<CreateMaintenanceLogInput>({
    resolver: zodResolver(createMaintenanceLogSchema),
    defaultValues: {
      log_date: new Date(),
      description: '',
      contractor_id: projectData?.contractor_id || '',
      pma_id: pmaCertificates[0]?.id || '',
    },
  });

  const handleSubmit = async (data: CreateMaintenanceLogInput) => {
    try {
      await onSubmit(data);
      form.reset();
    } catch (error) {
      // Error is handled by the parent component
      console.error('Form submission error:', error);
    }
  };

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(handleSubmit)}
        className="space-y-8 bg-white/60 backdrop-blur-sm rounded-xl border border-slate-200/60 p-6 shadow-xl shadow-slate-200/20"
      >
        {/* Log Date (read-only) */}
        <FormField
          control={form.control}
          name="log_date"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Log Date</FormLabel>
              <FormControl>
                <Input
                  value={format(field.value, 'PPpp')}
                  disabled
                  name={field.name}
                  onBlur={field.onBlur}
                  ref={field.ref}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Operation Type */}
        <FormField
          control={form.control}
          name="operation_log_type"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Operation Type</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select operation type" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {OPERATION_LOG_TYPES.map((type) => (
                    <SelectItem
                      key={type}
                      value={type}
                      className="flex items-center gap-2"
                    >
                      <MaintenanceTypeBadge type={type} />
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Status */}
        <FormField
          control={form.control}
          name="status"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Status</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {(
                    ['fully function', 'partially function', 'broken'] as const
                  ).map((status) => (
                    <SelectItem
                      key={status}
                      value={status}
                      className="flex items-center gap-2"
                    >
                      <span
                        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${
                          status === 'fully function'
                            ? 'bg-green-50 text-green-700 border-green-100'
                            : status === 'partially function'
                              ? 'bg-amber-50 text-amber-700 border-amber-100'
                              : 'bg-red-50 text-red-700 border-red-100'
                        }`}
                      >
                        {status}
                      </span>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Project Contractor */}
        {projectData && (
          <FormItem>
            <FormLabel>Project Contractor</FormLabel>
            <div className="rounded-lg border p-3 bg-slate-50/50">
              <p className="text-sm font-medium">
                {projectData.contractor_name}
              </p>
              <input
                type="hidden"
                {...form.register('contractor_id')}
                value={projectData.contractor_id}
              />
            </div>
          </FormItem>
        )}

        {/* PMA Certificate */}
        <FormField
          control={form.control}
          name="pma_id"
          render={({ field }) => (
            <FormItem>
              <FormLabel>PMA Certificate</FormLabel>
              <Select onValueChange={field.onChange} value={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select PMA certificate" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {pmaCertificates.length === 0 ? (
                    <SelectItem value="no-certificates" disabled>
                      No PMA certificates available
                    </SelectItem>
                  ) : (
                    pmaCertificates.map((cert) => {
                      const isExpired = new Date(cert.expiry_date) < new Date();
                      const isDraft = cert.pma_number.startsWith('Draft-');
                      return (
                        <SelectItem
                          key={cert.id}
                          value={cert.id}
                          className={cn(
                            'flex items-center justify-between',
                            isExpired && 'text-red-500',
                            isDraft && 'text-amber-600',
                          )}
                        >
                          <div className="flex-1">
                            <div className="flex items-center gap-2">
                              <span className="font-medium">
                                {cert.pma_number}
                              </span>
                              {isDraft && (
                                <span className="text-xs bg-amber-100 text-amber-700 px-1.5 py-0.5 rounded">
                                  {cert.status}
                                </span>
                              )}
                            </div>
                            {cert.location && (
                              <p className="text-xs text-muted-foreground">
                                📍 {cert.location}
                                {cert.state && `, ${cert.state}`}
                              </p>
                            )}
                          </div>
                          {isExpired && (
                            <AlertCircle className="h-4 w-4 text-red-500" />
                          )}
                        </SelectItem>
                      );
                    })
                  )}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Description */}
        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Enter maintenance log details..."
                  className="min-h-[120px]"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Submit Button */}
        <div className="flex justify-end">
          <Button
            type="submit"
            size="lg"
            disabled={isSubmitting}
            className="bg-gradient-to-r from-blue-500 to-indigo-600 text-white hover:from-blue-600 hover:to-indigo-700"
          >
            {isSubmitting ? 'Creating...' : 'Create Maintenance Log'}
          </Button>
        </div>
      </form>
    </Form>
  );
}
