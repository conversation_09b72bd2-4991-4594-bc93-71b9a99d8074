import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { cn } from '@/lib/utils';
import { format } from 'date-fns';
import {
  ArrowUpDown,
  Calendar,
  FileText,
  MoreHorizontal,
  Phone,
  User,
} from 'lucide-react';
import { useTranslations } from 'next-intl';
import type {
  MaintenanceLogsTableColumns,
  MaintenanceLogsTableProps,
  MaintenanceLogsType,
} from '../types/table';
import { DEFAULT_COLUMNS } from '../types/table';
import { MaintenanceTypeBadge } from './MaintenanceTypeBadge';

// Enhanced loading skeleton component with modern design
function TableSkeleton({
  columns,
}: {
  columns: MaintenanceLogsTableColumns[];
}) {
  return (
    <div className="space-y-4">
      {/* Header skeleton */}
      <div className="flex items-center space-x-4 p-6 bg-gradient-to-r from-slate-50 to-gray-50 rounded-xl border border-slate-100">
        {columns.map((column) => (
          <Skeleton
            key={column.key}
            className={cn(
              'h-5 bg-gradient-to-r from-slate-200 to-slate-300',
              column.key === 'description' ? 'w-48' : 'w-24',
            )}
          />
        ))}
      </div>

      {/* Row skeletons with staggered animation */}
      {Array.from({ length: 5 }).map((_, i) => (
        <div
          key={i}
          className="flex items-center space-x-4 p-6 bg-white rounded-xl border border-slate-100 hover:shadow-sm transition-all duration-300"
          style={{ animationDelay: `${i * 100}ms` }}
        >
          {columns.map((column) => (
            <Skeleton
              key={column.key}
              className={cn(
                'h-4 bg-gradient-to-r from-slate-100 to-slate-200',
                column.key === 'description' ? 'w-48' : 'w-24',
              )}
            />
          ))}
        </div>
      ))}
    </div>
  );
}

// Enhanced empty state component with modern design
function EmptyState({ message }: { message: string }) {
  return (
    <Card className="border-0 bg-gradient-to-br from-slate-50 via-white to-blue-50/20 shadow-sm">
      <div className="flex flex-col items-center justify-center py-20 text-center">
        {/* Animated icon with gradient background */}
        <div className="relative mb-6">
          <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-3xl blur-xl opacity-20 animate-pulse" />
          <div className="relative p-6 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-3xl border border-blue-100/50 shadow-sm">
            <FileText className="h-12 w-12 text-blue-600" />
          </div>
        </div>

        <h3 className="text-2xl font-bold text-gray-800 mb-3 tracking-tight">
          No maintenance logs found
        </h3>
        <p className="text-base text-gray-600 max-w-md leading-relaxed mb-6">
          {message}
        </p>

        {/* Decorative elements */}
        <div className="flex items-center space-x-2 text-xs text-gray-400">
          <div className="w-2 h-2 bg-blue-200 rounded-full animate-pulse" />
          <span>Try adjusting your filters or create a new log</span>
          <div
            className="w-2 h-2 bg-indigo-200 rounded-full animate-pulse"
            style={{ animationDelay: '0.5s' }}
          />
        </div>
      </div>
    </Card>
  );
}

export function MaintenanceLogsTable({
  data,
  isLoading,
  totalItems: _totalItems,
  tableState,
  onTableStateChange,
  columns = DEFAULT_COLUMNS,
}: MaintenanceLogsTableProps) {
  const t = useTranslations('pages.maintenanceLogs');
  const visibleColumns = (columns || DEFAULT_COLUMNS).filter(
    (col) => tableState.columnVisibility[col.key],
  ) as MaintenanceLogsTableColumns[];

  // Loading state
  if (isLoading) {
    return (
      <Card className="border-0 bg-gradient-to-br from-white via-slate-50/30 to-blue-50/10 shadow-lg backdrop-blur-sm">
        <div className="p-8">
          <TableSkeleton columns={visibleColumns} />
        </div>
      </Card>
    );
  }

  // Empty state
  if (data.length === 0) {
    return <EmptyState message={t('table.empty')} />;
  }

  return (
    <Card className="border-0 bg-gradient-to-br from-white via-slate-50/30 to-blue-50/10 shadow-lg backdrop-blur-sm overflow-hidden">
      <div className="overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow className="border-b border-slate-200/60 bg-gradient-to-r from-slate-50 via-white to-slate-50 hover:from-slate-100 hover:to-slate-100 transition-all duration-300">
              {visibleColumns.map((column) => (
                <TableHead
                  key={column.key}
                  className={cn(
                    'font-bold text-slate-700 h-14 px-8 text-sm tracking-wide',
                    column.className,
                    'cursor-pointer hover:bg-slate-100/50 transition-all duration-200 group',
                  )}
                  onClick={() =>
                    onTableStateChange?.({
                      ...tableState,
                      sorting: {
                        column: column.key,
                        direction:
                          tableState.sorting?.column === column.key &&
                          tableState.sorting.direction === 'asc'
                            ? 'desc'
                            : 'asc',
                      },
                    })
                  }
                >
                  <div className="flex items-center space-x-3">
                    <span className="group-hover:text-slate-900 transition-colors">
                      {t(`table.columns.${column.key}`, {
                        default: column.label,
                      })}
                    </span>
                    <ArrowUpDown className="h-4 w-4 text-slate-400 group-hover:text-slate-600 transition-colors" />
                  </div>
                </TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            {data.map((row, index) => (
              <TableRow
                key={row.id}
                className={cn(
                  'border-b border-slate-100/60 hover:bg-gradient-to-r hover:from-blue-50/30 hover:to-indigo-50/20 transition-all duration-300 group',
                  index % 2 === 0
                    ? 'bg-white'
                    : 'bg-gradient-to-r from-slate-50/30 to-white',
                )}
              >
                {visibleColumns.map((column) => (
                  <TableCell
                    key={`${row.id}-${column.key}`}
                    className={cn(
                      'px-8 py-6 text-slate-700 group-hover:text-slate-900 transition-colors',
                      column.className,
                    )}
                  >
                    {renderCell(row, column.key, t)}
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </Card>
  );
}

function renderCell(
  row: MaintenanceLogsType,
  key: keyof MaintenanceLogsType,
  t?: ReturnType<typeof useTranslations>,
) {
  if (key === 'operation_log_type') {
    return <MaintenanceTypeBadge type={row[key]} />;
  }

  if (key === 'status') {
    const statusConfig = {
      'fully function': {
        variant: 'default' as const,
        className:
          'bg-gradient-to-r from-emerald-50 to-green-50 text-emerald-700 border border-emerald-200/60 hover:from-emerald-100 hover:to-green-100 shadow-sm font-semibold',
        icon: '✓',
      },
      'partially function': {
        variant: 'secondary' as const,
        className:
          'bg-gradient-to-r from-amber-50 to-orange-50 text-amber-700 border border-amber-200/60 hover:from-amber-100 hover:to-orange-100 shadow-sm font-semibold',
        icon: '⚠',
      },
      broken: {
        variant: 'destructive' as const,
        className:
          'bg-gradient-to-r from-red-50 to-rose-50 text-red-700 border border-red-200/60 hover:from-red-100 hover:to-rose-100 shadow-sm font-semibold',
        icon: '✕',
      },
    };

    const config = statusConfig[row[key] as keyof typeof statusConfig] || {
      variant: 'outline' as const,
      className:
        'bg-gradient-to-r from-gray-50 to-slate-50 text-gray-700 border border-gray-200/60 shadow-sm',
      icon: '?',
    };

    return (
      <Badge
        variant={config.variant}
        className={cn(
          'px-3 py-1.5 text-xs transition-all duration-200',
          config.className,
        )}
      >
        <span className="mr-1.5">{config.icon}</span>
        {t
          ? t(`status.${row[key] as string}`, { default: row[key] })
          : row[key]}
      </Badge>
    );
  }

  if (key === 'log_date') {
    return (
      <div className="flex items-center space-x-3 text-sm">
        <div className="p-2 rounded-lg bg-gradient-to-br from-blue-50 to-indigo-50 border border-blue-100/50">
          <Calendar className="h-4 w-4 text-blue-600" />
        </div>
        <div className="flex flex-col">
          <span className="font-semibold text-slate-800">
            {format(new Date(row[key]), 'MMM dd, yyyy')}
          </span>
          <span className="text-xs text-slate-500">
            {format(new Date(row[key]), 'EEEE')}
          </span>
        </div>
      </div>
    );
  }

  if (key === 'contractor_name') {
    return (
      <div className="flex items-center space-x-3">
        <div className="p-2 rounded-xl bg-gradient-to-br from-purple-50 to-violet-50 border border-purple-100/50">
          <User className="h-4 w-4 text-purple-600" />
        </div>
        <div className="flex flex-col">
          <span className="font-semibold text-slate-800 text-sm">
            {row[key]}
          </span>
          <span className="text-xs text-slate-500">Contractor</span>
        </div>
      </div>
    );
  }

  if (key === 'person_in_charge_name') {
    return (
      <div className="space-y-2">
        <div className="flex items-center space-x-3">
          <div className="p-2 rounded-xl bg-gradient-to-br from-emerald-50 to-teal-50 border border-emerald-100/50">
            <User className="h-4 w-4 text-emerald-600" />
          </div>
          <div className="flex flex-col">
            <span className="font-semibold text-slate-800 text-sm">
              {row[key]}
            </span>
            <span className="text-xs text-slate-500">Person in Charge</span>
          </div>
        </div>
        {row.person_in_charge_phone && (
          <div className="flex items-center space-x-2 ml-11 text-xs">
            <div className="p-1 rounded-md bg-slate-100">
              <Phone className="h-3 w-3 text-slate-600" />
            </div>
            <span className="text-slate-600 font-medium">
              {row.person_in_charge_phone}
            </span>
          </div>
        )}
      </div>
    );
  }

  if (key === 'description') {
    return (
      <div className="max-w-sm">
        <p className="text-sm text-slate-700 line-clamp-2 leading-relaxed font-medium">
          {row[key]}
        </p>
        <div className="mt-1 text-xs text-slate-400">Click to expand</div>
      </div>
    );
  }

  if (key === 'pma_number') {
    return row[key] ? (
      <Badge
        variant="outline"
        className="font-mono text-xs bg-gradient-to-r from-slate-50 to-gray-50 text-slate-700 border-slate-200 hover:from-slate-100 hover:to-gray-100 transition-all duration-200 shadow-sm"
      >
        {row[key]}
      </Badge>
    ) : (
      <span className="text-xs text-slate-400 italic">No certificate</span>
    );
  }

  if (key === 'created_by') {
    return (
      <div className="flex items-center space-x-3 text-sm">
        <div className="p-2 rounded-lg bg-gradient-to-br from-purple-50 to-indigo-50 border border-purple-100/50">
          <User className="h-4 w-4 text-purple-600" />
        </div>
        <div className="flex flex-col">
          <span className="font-semibold text-slate-800">
            {row[key] || 'System'}
          </span>
          <span className="text-xs text-slate-500">Creator</span>
        </div>
      </div>
    );
  }

  if (key === 'actions') {
    return (
      <Button
        variant="ghost"
        size="sm"
        className="h-9 w-9 p-0 rounded-xl hover:bg-gradient-to-r hover:from-slate-100 hover:to-gray-100 transition-all duration-200 group"
      >
        <MoreHorizontal className="h-4 w-4 text-slate-500 group-hover:text-slate-700 transition-colors" />
        <span className="sr-only">Open menu</span>
      </Button>
    );
  }

  return <span className="text-sm font-medium text-slate-700">{row[key]}</span>;
}
