'use client';

import {
  Card,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  CardTitle,
} from '@/components/ui/card';
import { Activity, CalendarDays, TrendingUp } from 'lucide-react';
import { useMemo } from 'react';
import {
  Area,
  AreaChart,
  Bar,
  BarChart,
  Cell,
  Pie,
  PieChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from 'recharts';
import type { MaintenanceLogsType } from '../types/table';

interface MaintenanceChartsProps {
  data: MaintenanceLogsType[];
  isLoading?: boolean;
}

const COLORS = {
  primary: '#374151',
  success: '#374151',
  warning: '#6b7280',
  danger: '#374151',
  muted: '#9ca3af',
};

const STATUS_COLORS = {
  'fully function': '#374151',
  'partially function': '#6b7280',
  broken: '#9ca3af',
};

const OPERATION_COLORS = {
  'daily logs': '#374151',
  'second schedule': '#6b7280',
  mantrap: '#9ca3af',
};

export function MaintenanceCharts({ data, isLoading }: MaintenanceChartsProps) {
  const chartData = useMemo(() => {
    if (!data || data.length === 0) return null;

    // Weekly trend data (last 7 days)
    const weeklyData = [];
    const now = new Date();
    for (let i = 6; i >= 0; i--) {
      const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
      const dateStr = date.toISOString().split('T')[0];
      const count = data.filter(
        (log) => log.log_date.split('T')[0] === dateStr,
      ).length;

      weeklyData.push({
        date: date.toLocaleDateString('en-US', { weekday: 'short' }),
        count,
        fullDate: dateStr,
      });
    }

    // Status distribution
    const statusData = [
      {
        name: 'Fully Function',
        value: data.filter((log) => log.status === 'fully function').length,
        color: STATUS_COLORS['fully function'],
      },
      {
        name: 'Partially Function',
        value: data.filter((log) => log.status === 'partially function').length,
        color: STATUS_COLORS['partially function'],
      },
      {
        name: 'Broken',
        value: data.filter((log) => log.status === 'broken').length,
        color: STATUS_COLORS['broken'],
      },
    ].filter((item) => item.value > 0);

    // Operation type distribution
    const operationData = [
      {
        name: 'Daily Logs',
        value: data.filter((log) => log.operation_log_type === 'daily logs')
          .length,
        color: OPERATION_COLORS['daily logs'],
      },
      {
        name: 'Second Schedule',
        value: data.filter(
          (log) => log.operation_log_type === 'second schedule',
        ).length,
        color: OPERATION_COLORS['second schedule'],
      },
      {
        name: 'Mantrap',
        value: data.filter((log) => log.operation_log_type === 'mantrap')
          .length,
        color: OPERATION_COLORS['mantrap'],
      },
    ].filter((item) => item.value > 0);

    // Monthly trend (last 6 months)
    const monthlyData = [];
    for (let i = 5; i >= 0; i--) {
      const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
      const nextMonth = new Date(now.getFullYear(), now.getMonth() - i + 1, 1);
      const count = data.filter((log) => {
        const logDate = new Date(log.log_date);
        return logDate >= date && logDate < nextMonth;
      }).length;

      monthlyData.push({
        month: date.toLocaleDateString('en-US', { month: 'short' }),
        count,
        fullDate: date.toISOString(),
      });
    }

    return {
      weekly: weeklyData,
      status: statusData,
      operation: operationData,
      monthly: monthlyData,
    };
  }, [data]);

  if (isLoading) {
    return (
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        {Array.from({ length: 4 }).map((_, i) => (
          <Card key={i} className="border border-gray-200">
            <CardHeader className="pb-4">
              <div className="h-4 w-24 bg-gray-200 rounded animate-pulse" />
              <div className="h-3 w-32 bg-gray-100 rounded animate-pulse" />
            </CardHeader>
            <CardContent>
              <div className="h-32 bg-gray-100 rounded animate-pulse" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (!chartData) {
    return (
      <Card className="border border-gray-200">
        <CardContent className="flex items-center justify-center h-32">
          <p className="text-sm text-gray-500">No data available for charts</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
      {/* Weekly Trend Area Chart */}
      <Card className="border border-gray-200 md:col-span-2">
        <CardHeader className="pb-4">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-gray-100">
              <TrendingUp className="h-4 w-4 text-gray-600" />
            </div>
            <div>
              <CardTitle className="text-sm font-medium text-gray-900">
                Weekly Activity
              </CardTitle>
              <CardDescription className="text-xs text-gray-500 mt-1">
                Last 7 days maintenance logs
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent className="pt-0">
          <ResponsiveContainer width="100%" height={120}>
            <AreaChart data={chartData.weekly}>
              <defs>
                <linearGradient id="colorCount" x1="0" y1="0" x2="0" y2="1">
                  <stop
                    offset="5%"
                    stopColor={COLORS.primary}
                    stopOpacity={0.1}
                  />
                  <stop
                    offset="95%"
                    stopColor={COLORS.primary}
                    stopOpacity={0}
                  />
                </linearGradient>
              </defs>
              <XAxis
                dataKey="date"
                axisLine={false}
                tickLine={false}
                tick={{ fontSize: 11, fill: '#6b7280' }}
              />
              <YAxis hide />
              <Tooltip
                contentStyle={{
                  backgroundColor: 'white',
                  border: '1px solid #e5e7eb',
                  borderRadius: '8px',
                  fontSize: '12px',
                }}
              />
              <Area
                type="monotone"
                dataKey="count"
                stroke={COLORS.primary}
                strokeWidth={1.5}
                fillOpacity={1}
                fill="url(#colorCount)"
              />
            </AreaChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Status Distribution */}
      <Card className="border border-gray-200">
        <CardHeader className="pb-4">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-gray-100">
              <Activity className="h-4 w-4 text-gray-600" />
            </div>
            <div>
              <CardTitle className="text-sm font-medium text-gray-900">
                Status Health
              </CardTitle>
              <CardDescription className="text-xs text-gray-500 mt-1">
                Equipment status distribution
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent className="pt-0">
          <ResponsiveContainer width="100%" height={120}>
            <PieChart>
              <Pie
                data={chartData.status}
                cx="50%"
                cy="50%"
                innerRadius={25}
                outerRadius={50}
                paddingAngle={2}
                dataKey="value"
              >
                {chartData.status.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip
                contentStyle={{
                  backgroundColor: 'white',
                  border: '1px solid #e5e7eb',
                  borderRadius: '8px',
                  fontSize: '12px',
                }}
              />
            </PieChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Operation Types */}
      <Card className="border border-gray-200">
        <CardHeader className="pb-4">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-gray-100">
              <CalendarDays className="h-4 w-4 text-gray-600" />
            </div>
            <div>
              <CardTitle className="text-sm font-medium text-gray-900">
                Operation Types
              </CardTitle>
              <CardDescription className="text-xs text-gray-500 mt-1">
                Maintenance categories
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent className="pt-0">
          <ResponsiveContainer width="100%" height={120}>
            <BarChart data={chartData.operation} layout="horizontal">
              <XAxis type="number" hide />
              <YAxis
                type="category"
                dataKey="name"
                axisLine={false}
                tickLine={false}
                tick={{ fontSize: 10, fill: '#6b7280' }}
                width={60}
              />
              <Tooltip
                contentStyle={{
                  backgroundColor: 'white',
                  border: '1px solid #e5e7eb',
                  borderRadius: '8px',
                  fontSize: '12px',
                }}
              />
              <Bar dataKey="value" radius={[0, 4, 4, 0]}>
                {chartData.operation.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Bar>
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>
    </div>
  );
}
