import { useUserWithProfile } from '@/hooks/use-auth';
import { toast } from '@/hooks/use-toast';
import { useProjectContext } from '@/providers/project-context';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { processBatchInvitations } from '../services/invitation-service';
import type {
  BatchInvitationResult,
  InvitationMember,
} from '../types/invitation';

/**
 * Hook to invite existing users to a project
 */
export function useInviteExistingUsers() {
  const queryClient = useQueryClient();
  const { data: user } = useUserWithProfile();
  const { selectedProjectId } = useProjectContext();

  return useMutation({
    mutationKey: ['invite-existing-users'],
    mutationFn: async (
      members: InvitationMember[],
    ): Promise<BatchInvitationResult> => {
      if (!user?.id) {
        throw new Error('User not authenticated');
      }

      if (!selectedProjectId) {
        throw new Error('No project selected');
      }

      // Extract emails and use 'technician' role for all (as per Phase 1 requirements)
      const emails = members.map((member) => member.email);

      return processBatchInvitations(
        emails,
        selectedProjectId,
        'technician', // Fixed role for Phase 1
        user.id,
      );
    },
    onSuccess: (result: BatchInvitationResult) => {
      // Invalidate project-related queries to refresh data
      queryClient.invalidateQueries({ queryKey: ['projects'] });
      queryClient.invalidateQueries({ queryKey: ['accessible-projects'] });
      queryClient.invalidateQueries({
        queryKey: ['projects', selectedProjectId],
      });

      // Show success toast with summary
      if (result.successCount > 0) {
        toast({
          title: 'Members Added Successfully',
          description: `${result.successCount} member(s) have been added to the project.`,
        });
      }

      // Show warnings for issues (but don't treat as errors)
      if (result.alreadyAddedCount > 0) {
        toast({
          title: 'Some Members Already Added',
          description: `${result.alreadyAddedCount} member(s) were already in the project.`,
          variant: 'default', // Not an error, just info
        });
      }

      if (result.userNotFoundCount > 0) {
        toast({
          title: 'Some Users Not Found',
          description: `${result.userNotFoundCount} email(s) were not found in the system.`,
          variant: 'default',
        });
      }
    },
    onError: (error) => {
      console.error('Error inviting users:', error);

      toast({
        title: 'Failed to Invite Members',
        description:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred.',
        variant: 'destructive',
      });
    },
  });
}
