import { useMutation } from '@tanstack/react-query';
import { useTranslations } from 'next-intl';
import { toast } from 'sonner';
import {
  acceptInvitation,
  getInvitationByToken,
  type AcceptInvitationResult,
  type InvitationWithProject,
} from '../services/acceptInvitation';

/**
 * Hook for getting invitation details by token
 */
export function useGetInvitation(token: string) {
  return useMutation<
    { invitation: InvitationWithProject | null; error?: string },
    Error,
    void
  >({
    mutationFn: () => getInvitationByToken(token),
  });
}

/**
 * Hook for accepting a project invitation
 */
export function useAcceptInvitation() {
  const t = useTranslations();

  return useMutation<
    AcceptInvitationResult,
    Error,
    { token: string; userId: string }
  >({
    mutationFn: ({ token, userId }) => acceptInvitation(token, userId),
    onSuccess: (data) => {
      if (data.success) {
        toast.success(
          t('invitation.accept.success', {
            projectName: data.projectName || 'Unknown Project',
          }),
        );
      } else {
        toast.error(
          t('invitation.accept.error', {
            error: data.error || 'Unknown error',
          }),
        );
      }
    },
    onError: (error) => {
      console.error('Failed to accept invitation:', error);
      toast.error(
        t('invitation.accept.error', {
          error: error.message,
        }),
      );
    },
  });
}
