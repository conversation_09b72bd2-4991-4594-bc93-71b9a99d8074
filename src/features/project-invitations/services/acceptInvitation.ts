import { supabase } from '@/lib/supabase';
import type { ProjectInvitation } from '../types/invitation';
import { mapProjectRoleToUserRole } from '../utils/role-mapping';

export interface AcceptInvitationResult {
  success: boolean;
  projectId?: string;
  projectName?: string;
  error?: string;
}

export interface InvitationWithProject extends ProjectInvitation {
  project?: {
    id: string;
    name: string;
  };
}

/**
 * Get invitation details by token
 */
export async function getInvitationByToken(token: string): Promise<{
  invitation: InvitationWithProject | null;
  error?: string;
}> {
  try {
    const { data, error } = await supabase
      .from('project_invitations')
      .select(
        `
        *,
        project:projects(id, name)
      `,
      )
      .eq('token', token)
      .eq('status', 'pending')
      .gt('expiry_date', new Date().toISOString())
      .maybeSingle();

    if (error) {
      console.error('Error fetching invitation:', error);
      return { invitation: null, error: error.message };
    }

    if (!data) {
      return { invitation: null, error: 'Invalid or expired invitation' };
    }

    return { invitation: data };
  } catch (error) {
    console.error('Error in getInvitationByToken:', error);
    return {
      invitation: null,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Accept a project invitation
 * This creates the user account if needed and adds them to the project
 */
export async function acceptInvitation(
  token: string,
  userId: string,
): Promise<AcceptInvitationResult> {
  try {
    // Get invitation details
    const { invitation, error: inviteError } =
      await getInvitationByToken(token);

    if (!invitation || inviteError) {
      return {
        success: false,
        error: inviteError || 'Invitation not found',
      };
    }

    // Step 1: Ensure user exists in the users table (create if needed)
    const { data: existingUser, error: userCheckError } = await supabase
      .from('users')
      .select('id')
      .eq('id', userId)
      .maybeSingle();

    if (userCheckError && userCheckError.code !== 'PGRST116') {
      console.error('Error checking user existence:', userCheckError);
      return {
        success: false,
        error: 'Failed to verify user account',
      };
    }

    // If user doesn't exist in users table, create them
    if (!existingUser) {
      console.log('Creating user profile in users table...');

      // Get current user data from the session
      const { data: authUser, error: userError } =
        await supabase.auth.getUser();

      if (userError || !authUser.user) {
        console.error('No authenticated user found:', userError);
        return {
          success: false,
          error: 'User session not found',
        };
      }

      // Map project role to user role using centralized mapping
      const userRole = mapProjectRoleToUserRole(invitation.role);

      // Get contractor_id from the project for contractor users
      let contractorId: string | null = null;
      if (userRole === 'contractor') {
        const { data: project, error: projectError } = await supabase
          .from('projects')
          .select('contractor_id')
          .eq('id', invitation.project_id)
          .single();

        if (projectError) {
          console.error('Error fetching project contractor_id:', projectError);
          return {
            success: false,
            error: 'Failed to get project details',
          };
        }

        contractorId = project?.contractor_id || null;
      }

      const { error: createUserError } = await supabase.from('users').insert({
        id: userId,
        email: authUser.user.email || invitation.invitee_email,
        name:
          authUser.user.user_metadata?.full_name ||
          authUser.user.email?.split('@')[0] ||
          'User',
        phone_number: authUser.user.user_metadata?.phone_number || null,
        user_role: userRole,
        contractor_id: contractorId,
        onboarding_completed: false,
        created_at: new Date().toISOString(),
      });

      if (createUserError) {
        console.error('Error creating user profile:', createUserError);
        return {
          success: false,
          error: 'Failed to create user profile',
        };
      }
      console.log('User profile created successfully');
    }

    // Step 2: Add user to project_users
    const { data: _projectUser, error: addUserError } = await supabase
      .from('project_users')
      .insert({
        user_id: userId,
        project_id: invitation.project_id,
        role: invitation.role,
        assigned_date: new Date().toISOString().split('T')[0],
        is_active: true,
        created_by: invitation.inviter_user_id,
        created_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (addUserError) {
      console.error('Error adding user to project:', addUserError);
      return {
        success: false,
        error: 'Failed to add user to project',
      };
    }

    // Step 3: Update invitation status
    const { error: updateError } = await supabase
      .from('project_invitations')
      .update({
        status: 'accepted',
        invitee_user_id: userId,
        responded_at: new Date().toISOString(),
        responded_by: userId,
      })
      .eq('token', token);

    if (updateError) {
      console.error('Error updating invitation status:', updateError);
      // Don't fail here - the user was already added to the project
    }

    // Get project details for response
    const { data: project } = await supabase
      .from('projects')
      .select('name')
      .eq('id', invitation.project_id)
      .single();

    return {
      success: true,
      projectId: invitation.project_id,
      projectName: project?.name || 'Unknown Project',
    };
  } catch (error) {
    console.error('Error in acceptInvitation:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}
