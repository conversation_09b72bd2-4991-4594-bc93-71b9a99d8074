'use client';

import {
  clearProjectIdFromCookies,
  getProjectIdFromCookies,
  setProjectIdInCookies
} from '@/lib/middleware-utils';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { createContext, ReactNode, useContext } from 'react';

interface ProjectContextValue {
  selectedProjectId: string | null;
  selectProject: (projectId: string) => void;
  clearProject: () => void;
  isInProjectContext: boolean;
  isInitialized: boolean;
}

const ProjectContext = createContext<ProjectContextValue | undefined>(
  undefined,
);

interface ProjectContextProviderProps {
  children: ReactNode;
}

export function ProjectContextProvider({
  children,
}: ProjectContextProviderProps) {
  const queryClient = useQueryClient();

  // Query to get selected project ID from cookies
  const { data: selectedProjectId, isSuccess: isInitialized } = useQuery({
    queryKey: ['selectedProject'],
    queryFn: () => getProjectIdFromCookies(),
    staleTime: Infinity,
    gcTime: Infinity,
  });

  // Mutation to select a project
  const selectProjectMutation = useMutation({
    mutationFn: (projectId: string) => {
      setProjectIdInCookies(projectId);
      return Promise.resolve(projectId);
    },
    onSuccess: (projectId) => {
      queryClient.setQueryData(['selectedProject'], projectId);
    },
  });

  // Mutation to clear project
  const clearProjectMutation = useMutation({
    mutationFn: () => {
      clearProjectIdFromCookies();
      return Promise.resolve(null);
    },
    onSuccess: () => {
      queryClient.setQueryData(['selectedProject'], null);
    },
  });

  const selectProject = (projectId: string) => {
    selectProjectMutation.mutate(projectId);
  };

  const clearProject = () => {
    clearProjectMutation.mutate();
  };

  const isInProjectContext = selectedProjectId !== null && selectedProjectId !== undefined;

  return (
    <ProjectContext.Provider
      value={{
        selectedProjectId: selectedProjectId ?? null,
        selectProject,
        clearProject,
        isInProjectContext,
        isInitialized,
      }}
    >
      {children}
    </ProjectContext.Provider>
  );
}

export function useProjectContext() {
  const context = useContext(ProjectContext);
  if (context === undefined) {
    throw new Error(
      'useProjectContext must be used within a ProjectContextProvider',
    );
  }
  return context;
}
