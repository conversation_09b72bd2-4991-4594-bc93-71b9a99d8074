import { getRequestConfig } from 'next-intl/server';
import { defaultLocale, locales, type Locale } from './config';

export default getRequestConfig(async ({ locale }) => {
  // Validate that the incoming locale is supported
  const validatedLocale = locales.includes(locale as Locale)
    ? (locale as Locale)
    : defaultLocale;
  try {
    const messages = (await import(`../../messages/${validatedLocale}.json`))
      .default;
    return {
      locale: validatedLocale,
      messages,
    };
  } catch {
    // Fallback to default locale
    const fallbackMessages = (
      await import(`../../messages/${defaultLocale}.json`)
    ).default;
    return {
      locale: defaultLocale,
      messages: fallbackMessages,
    };
  }
});
