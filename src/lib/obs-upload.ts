export interface OBSUploadConfig {
  file: File;
  folder?: string;
  onProgress?: (progress: number) => void;
}

export async function uploadToOBS({
  file,
  folder = '',
  onProgress,
}: OBSUploadConfig): Promise<string> {
  try {
    const formData = new FormData();
    formData.append('file', file);
    if (folder) {
      formData.append('folder', folder);
    }

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

    // Use XMLHttpRequest for progress tracking
    return new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest();

      // Track upload progress
      xhr.upload.addEventListener('progress', (event) => {
        if (event.lengthComputable && onProgress) {
          const progress = Math.round((event.loaded / event.total) * 100);
          onProgress(progress);
        }
      });

      xhr.addEventListener('load', async () => {
        clearTimeout(timeoutId);

        try {
          const data = JSON.parse(xhr.responseText);

          if (xhr.status >= 200 && xhr.status < 300) {
            resolve(data.url);
          } else {
            reject(
              new Error(
                data.error || `Upload failed with status ${xhr.status}`,
              ),
            );
          }
        } catch {
          reject(new Error('Failed to parse server response'));
        }
      });

      xhr.addEventListener('error', () => {
        clearTimeout(timeoutId);
        reject(new Error('Network error during upload'));
      });

      xhr.addEventListener('abort', () => {
        clearTimeout(timeoutId);
        reject(new Error('Upload timed out after 30 seconds'));
      });

      xhr.open('POST', '/api/upload');
      xhr.send(formData);

      // Handle timeout
      controller.signal.addEventListener('abort', () => {
        xhr.abort();
      });
    });
  } catch (error) {
    throw new Error(
      `Failed to upload file to OBS: ${error instanceof Error ? error.message : 'Unknown error'}`,
    );
  }
}
