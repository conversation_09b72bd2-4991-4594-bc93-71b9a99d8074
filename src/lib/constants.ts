/**
 * Shared constants for the application
 * These constants are used across multiple components to maintain consistency
 */

// Mantrap location options for damage complaints and lift management
export const MANTRAP_LOCATIONS = [
  'Main Entrance',
  'Side Entrance',
  'Emergency Exit',
  'Loading Bay',
  'Service Entrance',
  'Parking Entrance',
  'Other',
] as const;

// Agency codes that correspond to translation keys in messages/en.json and messages/ms.json
// These should be used with the agencies translation namespace
export const AGENCY_CODES = [
  // Federal Government Ministries
  'JKR',
  'KKM',
  'KPM',
  'KPKT',
  'KKR',
  'KPDNHEP',
  'MOSTI',
  'KPWKM',

  // State Government Agencies
  'DBKL',
  'MBPJ',
  'MBSJ',
  'MBSA',
  'MPK',
  'MBIP',
  'MPJB',
  'MBPP',
  'MPSP',
  'MBMB',
  'MPAG',

  // Statutory Bodies
  'TNB',
  'SYABAS',
  'IWK',
  'PLUS',
  'KTMB',
  'MRT',
  'LRT',

  // Healthcare
  'HSB',
  'HKL',
  'HUSM',
  'HUKM',

  // Education
  'UM',
  'UKM',
  'USM',
  'UTM',
  'UPM',
  'UiTM',

  // Other (for cases not covered above)
  'Other',
] as const;

// Type definitions for type safety
export type MantrapLocation = (typeof MANTRAP_LOCATIONS)[number];
export type AgencyCode = (typeof AGENCY_CODES)[number];
