import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Generates a unique alphanumeric code for companies
 * Format: XXXX-YYYY-ZZZZ (12 characters in 3 groups of 4, using A-Z and 0-9)
 * This provides 36^12 = ~4.7 x 10^18 possibilities, making collisions extremely rare
 */
export function generateCompanyCode(): string {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';

  // Generate timestamp-based prefix for better uniqueness
  const now = new Date();
  const year = now.getFullYear().toString().slice(-2);
  const month = (now.getMonth() + 1).toString().padStart(2, '0');

  // Generate random suffix
  const generateRandomPart = (length: number): string => {
    let result = '';
    for (let i = 0; i < length; i++) {
      result += characters.charAt(
        Math.floor(Math.random() * characters.length),
      );
    }
    return result;
  };

  // Format: YY + MM + 8 random characters = 12 characters total
  const datePart = year + month;
  const randomPart = generateRandomPart(8);

  // Split into groups of 4 for better readability: YYMM-XXXX-XXXX
  const code = datePart + randomPart;
  return `${code.slice(0, 4)}-${code.slice(4, 8)}-${code.slice(8, 12)}`;
}
