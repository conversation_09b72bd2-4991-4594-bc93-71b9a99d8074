import type { ProjectRole } from '@/features/project-invitations/types/invitation';

export interface EmailTemplateData {
  inviteeEmail: string;
  role: ProjectRole;
  token: string;
  projectName: string;
  inviterName: string;
  appUrl: string;
}

/**
 * Generate HTML email template for project invitations
 */
export function generateInvitationEmailHtml(data: EmailTemplateData): string {
  const { inviteeEmail, role, token, projectName, inviterName, appUrl } = data;
  const invitationUrl = `${appUrl}/invite?token=${token}`;

  return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Project Invitation</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .email-container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #2563eb;
        }
        .content {
            margin-bottom: 30px;
        }
        .invitation-button {
            display: inline-block;
            background-color: #2563eb;
            color: white;
            padding: 12px 30px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: bold;
            margin: 20px 0;
        }
        .invitation-button:hover {
            background-color: #1d4ed8;
        }
        .role-badge {
            background-color: #f3f4f6;
            color: #374151;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
            text-transform: capitalize;
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
            font-size: 14px;
            color: #6b7280;
        }
        .expiry-notice {
            background-color: #fef3c7;
            border: 1px solid #f59e0b;
            color: #92400e;
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header">
            <div class="logo">SimPLE</div>
        </div>
        
        <div class="content">
            <h2>You've been invited to join a project!</h2>
            
            <p>Hi ${inviteeEmail},</p>
            
            <p><strong>${inviterName}</strong> has invited you to join the project <strong>"${projectName}"</strong> as a <span class="role-badge">${role}</span>.</p>
            
            <p>Click the button below to accept your invitation and get started:</p>
            
            <div style="text-align: center;">
                <a href="${invitationUrl}" class="invitation-button">Accept Invitation</a>
            </div>
            
            <div class="expiry-notice">
                <strong>⏰ Important:</strong> This invitation link expires in 7 days. Please accept it before then to join the project.
            </div>
            
            <p>If you're unable to click the button, you can copy and paste this link into your browser:</p>
            <p style="word-break: break-all; color: #2563eb;">${invitationUrl}</p>
        </div>
        
        <div class="footer">
            <p>This invitation was sent by ${inviterName} through SimPLE. If you didn't expect this invitation, you can safely ignore this email.</p>
            <p><strong>SimPLE Team</strong></p>
        </div>
    </div>
</body>
</html>`;
}

/**
 * Generate plain text email template for project invitations
 */
export function generateInvitationEmailText(data: EmailTemplateData): string {
  const { inviteeEmail, role, token, projectName, inviterName, appUrl } = data;
  const invitationUrl = `${appUrl}/invite?token=${token}`;

  return `
You've been invited to join a project!

Hi ${inviteeEmail},

${inviterName} has invited you to join the project "${projectName}" as a ${role}.

Click the link below to accept your invitation:
${invitationUrl}

IMPORTANT: This invitation link expires in 7 days.

If you didn't expect this invitation, you can safely ignore this email.

Thanks,
The SimPLE Team
`;
}
