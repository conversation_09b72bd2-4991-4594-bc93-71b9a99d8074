/**
 * Utility functions for middleware
 */

import type { UserRole } from '@/types/auth';
import { NextRequest, NextResponse } from 'next/server';
import { ROUTE_PERMISSIONS } from './rbac';

// Cookie configuration
const COOKIE_CONFIG = {
  path: '/',
  httpOnly: true,
  secure: process.env.NODE_ENV === 'production',
  sameSite: 'lax' as const,
  maxAge: 60 * 60 * 24, // 24 hours
};

/**
 * Clear role cache for a specific user by setting a cookie
 * This can be called from client-side code when role changes occur
 */
export function clearUserRoleCache(userId: string) {
  if (typeof document !== 'undefined') {
    // Set a cookie that the middleware can check to clear the cache
    document.cookie = `clear_role_cache_${userId}=true; path=/; max-age=60`;
  }
}

/**
 * Set user role and onboarding status in cookies
 */
export function setUserCookies(
  response: NextResponse,
  userRole: UserRole,
  onboardingCompleted: boolean,
) {
  response.cookies.set('user_role', userRole, COOKIE_CONFIG);
  response.cookies.set(
    'onboarding_completed',
    onboardingCompleted.toString(),
    COOKIE_CONFIG,
  );
}

/**
 * Set project cookie for persistence across sessions
 */
export function setProjectCookie(
  response: NextResponse,
  projectId: string,
) {
  response.cookies.set('selected_project_id', projectId, {
    httpOnly: false, // Needs client access
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax' as const,
    maxAge: 60 * 60 * 24 * 30, // 30 days
  });
}

/**
 * Clear project cookie
 */
export function clearProjectCookie(response: NextResponse) {
  response.cookies.delete('selected_project_id');
}

/**
 * Get user data from cookies
 */
export function getUserDataFromCookies(req: NextRequest) {
  const userRole = req.cookies.get('user_role')?.value as UserRole;
  const onboardingCompleted =
    req.cookies.get('onboarding_completed')?.value === 'true';

  return { userRole, onboardingCompleted };
}

/**
 * Get project ID from cookies (client-side)
 */
export function getProjectIdFromCookies(): string | null {
  if (typeof document === 'undefined') return null;

  const cookies = document.cookie.split(';');
  const projectCookie = cookies.find(cookie =>
    cookie.trim().startsWith('selected_project_id=')
  );

  return projectCookie ? projectCookie.split('=')[1] : null;
}

/**
 * Set project ID in cookies (client-side)
 */
export function setProjectIdInCookies(projectId: string) {
  if (typeof document === 'undefined') return;

  const maxAge = 60 * 60 * 24 * 30; // 30 days
  const secure = process.env.NODE_ENV === 'production' ? '; Secure' : '';

  document.cookie = `selected_project_id=${projectId}; path=/; max-age=${maxAge}; SameSite=Lax${secure}`;
}

/**
 * Clear project ID from cookies (client-side)
 */
export function clearProjectIdFromCookies() {
  if (typeof document === 'undefined') return;

  document.cookie = 'selected_project_id=; path=/; max-age=0';
}

/**
 * Check if a path requires authentication
 */
export function isProtectedPath(pathname: string): boolean {
  const protectedPaths = [
    '/projects',
    '/dashboard',
    '/profile',
    '/analytics',
    '/reports',
    '/settings',
    '/users',
    '/buildings',
    '/contractors',
  ];

  // Handle admin paths specially - protect admin dashboard but not auth routes
  if (pathname.startsWith('/admin')) {
    const authPaths = ['/admin/login', '/admin/register'];
    return !authPaths.some((authPath) => pathname.startsWith(authPath));
  }

  return protectedPaths.some((path) => pathname.startsWith(path));
}

/**
 * Check if a path is an auth route
 */
export function isAuthPath(pathname: string): boolean {
  const authPaths = [
    '/admin/login',
    '/admin/register',
    '/contractor/login',
    '/contractor/register',
  ];
  return authPaths.some((path) => pathname.startsWith(path));
}

/**
 * Check if a path is an onboarding route
 */
export function isOnboardingPath(pathname: string): boolean {
  const onboardingPaths = ['/onboarding', '/profile'];
  return onboardingPaths.some((path) => pathname.startsWith(path));
}

/**
 * Check if user has permission for a route
 */
export function hasRoutePermission(
  pathname: string,
  userRole: UserRole,
): boolean {
  // Sort routes by path length (descending) to match more specific routes first
  const sortedRoutes = [...ROUTE_PERMISSIONS].sort(
    (a, b) => b.path.length - a.path.length,
  );

  const routePermission = sortedRoutes.find((route) =>
    pathname.startsWith(route.path),
  );

  return !routePermission || routePermission.roles.includes(userRole);
}

/**
 * Create redirect URL with original path as redirectTo parameter
 */
export function createRedirectUrl(
  baseUrl: string,
  originalPath: string,
  search: string,
  requestUrl?: string,
): URL {
  // If baseUrl is relative and requestUrl is provided, create absolute URL
  const url =
    baseUrl.startsWith('/') && requestUrl
      ? new URL(baseUrl, requestUrl)
      : new URL(baseUrl);
  url.searchParams.set('redirectTo', originalPath + search);
  return url;
}

/**
 * Create safe redirect URL (prevents open redirect attacks)
 */
export function createSafeRedirectUrl(
  redirectTo: string | null,
  baseUrl: string,
  requestUrl?: string,
): URL {
  if (
    redirectTo &&
    redirectTo.startsWith('/') &&
    !redirectTo.startsWith('//')
  ) {
    // If baseUrl is relative and requestUrl is provided, create absolute URL
    const absoluteBaseUrl =
      baseUrl.startsWith('/') && requestUrl
        ? new URL(baseUrl, requestUrl).href
        : baseUrl;
    return new URL(redirectTo, absoluteBaseUrl);
  }

  // If baseUrl is relative and requestUrl is provided, create absolute URL
  const url =
    baseUrl.startsWith('/') && requestUrl
      ? new URL(baseUrl, requestUrl)
      : new URL(baseUrl);
  return url;
}

/**
 * Check if role caching is enabled
 */
export const ROLE_CACHE_ENABLED = process.env.DISABLE_ROLE_CACHE !== 'true';

/**
 * Role cache duration in milliseconds (5 minutes by default)
 */
export const ROLE_CACHE_DURATION = parseInt(
  process.env.ROLE_CACHE_DURATION || '300000',
);

/**
 * Check for cache clearing cookies and clear cache if needed
 * This function should be called from the middleware
 */
export function handleCacheClearingCookies(
  req: {
    cookies: {
      get: (name: string) => { value: string } | undefined;
      getAll: () => Array<{ name: string; value: string }>;
    };
  },
  roleCache: Map<string, { role: string; timestamp: number }>,
) {
  const cookies = req.cookies;

  // Check for clear all cache cookie
  if (cookies.get('clear_all_role_cache')?.value === 'true') {
    roleCache.clear();
    return true;
  }

  // Check for individual user cache clearing
  const allCookies = cookies.getAll();
  for (const cookie of allCookies) {
    if (
      cookie.name.startsWith('clear_role_cache_') &&
      cookie.value === 'true'
    ) {
      const userId = cookie.name.replace('clear_role_cache_', '');
      roleCache.delete(userId);
    }
  }

  return false;
}

/**
 * Fetch user profile data from database
 */
export async function fetchUserProfile(
  supabase: ReturnType<typeof import('@supabase/ssr').createServerClient>,
  userId: string,
) {
  try {
    const { data: profile, error } = await supabase
      .from('users')
      .select('user_role, onboarding_completed')
      .eq('id', userId)
      .single();

    if (error) throw error;

    return {
      userRole: profile?.user_role as UserRole,
      onboardingCompleted: profile?.onboarding_completed ?? false,
    };
  } catch (error) {
    console.error('Error fetching user profile:', error);
    return null;
  }
}

/**
 * Clear all role cache entries by setting a cookie
 */
export function clearAllRoleCache() {
  if (typeof document !== 'undefined') {
    document.cookie = `clear_all_role_cache=true; path=/; max-age=60`;
  }
}
