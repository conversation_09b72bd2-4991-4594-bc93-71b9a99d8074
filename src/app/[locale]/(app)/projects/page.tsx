'use client';

import {
  PendingInvitations,
  ProjectList,
  ProjectStatsCards,
  useProjects,
  useProjectStats,
} from '@/features/projects';
import { CreateProjectButton } from '@/features/projects/components/create-project-button';
import { FolderOpen } from 'lucide-react';
import { useTranslations } from 'next-intl';

const ProjectsPage = () => {
  const t = useTranslations('pages.projects');

  const { data: projects = [], isLoading: projectsLoading } = useProjects();
  const { data: stats, isLoading: statsLoading } = useProjectStats();

  return (
    <div className="container mx-auto py-6">
      <div className="space-y-8">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div className="flex items-center space-x-4">
            <div className="p-3 rounded-xl bg-primary/10">
              <FolderOpen className="h-8 w-8 text-primary" />
            </div>
            <div>
              <h1 className="text-3xl font-bold tracking-tight text-gray-900">
                {t('title')}
              </h1>
              <p className="text-gray-500 mt-1">{t('description')}</p>
            </div>
          </div>
          <CreateProjectButton />
        </div>

        {/* Stats Cards */}
        {stats && <ProjectStatsCards stats={stats} isLoading={statsLoading} />}

        {/* Pending Invitations */}
        <PendingInvitations />

        {/* Projects List */}
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold text-gray-900">
              Recent Projects
            </h2>
          </div>

          <ProjectList projects={projects} isLoading={projectsLoading} />
        </div>
      </div>
    </div>
  );
};

export default ProjectsPage;
