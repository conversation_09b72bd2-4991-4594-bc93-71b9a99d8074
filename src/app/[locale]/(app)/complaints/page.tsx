'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
  Drawer,
  DrawerContent,
  DrawerDes<PERSON>,
  DrawerHeader,
  Drawer<PERSON><PERSON><PERSON>,
  DrawerTrigger,
} from '@/components/ui/drawer';
import { ComplaintDetails } from '@/features/complaints/components/complaint-details-simple';
import { ComplaintsTable } from '@/features/complaints/components/complaints-table-simple';
import { DamageComplaintForm } from '@/features/complaints/components/damage-complaint-form';
import { ComplaintUI } from '@/features/complaints/types/ui-types';
import { useProject } from '@/features/projects';
import { useProjectContext } from '@/providers/project-context';
import { Plus } from 'lucide-react';
import { useState } from 'react';

export default function ComplaintsPage() {
  const { selectedProjectId, isInProjectContext } = useProjectContext();
  const { data: project } = useProject(selectedProjectId || '');
  const [selectedComplaint, setSelectedComplaint] =
    useState<ComplaintUI | null>(null);
  const [isFormDrawerOpen, setIsFormDrawerOpen] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);

  const handleViewComplaint = (complaint: ComplaintUI) => {
    setSelectedComplaint(complaint);
    setIsEditMode(false);
  };
  const handleEditComplaint = () => {
    setIsEditMode(true);
    setIsFormDrawerOpen(true);
  };

  const handleEditComplaintFromTable = (complaint: ComplaintUI) => {
    setSelectedComplaint(complaint);
    setIsEditMode(true);
    setIsFormDrawerOpen(true);
  };

  const handleCloseDetails = () => {
    setSelectedComplaint(null);
    setIsEditMode(false);
  };

  const handleFormSuccess = () => {
    setIsFormDrawerOpen(false);
    setIsEditMode(false);
    // Optionally refresh the table data here
  };

  const handleFormCancel = () => {
    setIsFormDrawerOpen(false);
    setIsEditMode(false);
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            {isInProjectContext && project
              ? `${project.name} - Complaints`
              : 'Damage Complaint Logs'}
          </h1>
          <p className="text-gray-600 mt-1">
            {isInProjectContext && project
              ? `Manage complaints for ${project.name} (${project.code})`
              : 'Manage and track damage complaint logs and repair status'}
          </p>
        </div>{' '}
        <Drawer open={isFormDrawerOpen} onOpenChange={setIsFormDrawerOpen}>
          <DrawerTrigger asChild>
            <Button className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              New Complaint
            </Button>
          </DrawerTrigger>
          <DrawerContent className="max-h-[90vh]">
            <div className="mx-auto w-full max-w-4xl">
              <DrawerHeader>
                <DrawerTitle>
                  {isEditMode
                    ? `Edit Complaint #${selectedComplaint?.complaintNumber}`
                    : 'Create New Damage Complaint Log'}
                </DrawerTitle>
                <DrawerDescription>
                  {isEditMode
                    ? 'Update the complaint details below.'
                    : 'Fill in the details below to create a new damage complaint log entry.'}
                </DrawerDescription>
              </DrawerHeader>
              <div className="p-4 pb-8 overflow-y-auto max-h-[calc(90vh-120px)]">
                <DamageComplaintForm
                  onSuccess={handleFormSuccess}
                  onCancel={handleFormCancel}
                  editMode={isEditMode}
                  initialData={
                    isEditMode && selectedComplaint
                      ? {
                          email: selectedComplaint.email,
                          damageComplaintDate: new Date(
                            selectedComplaint.damageComplaintDate,
                          ),
                          expectedCompletionDate: new Date(
                            selectedComplaint.expectedCompletionDate,
                          ),
                          actualCompletionDate:
                            selectedComplaint.actualCompletionDate
                              ? new Date(selectedComplaint.actualCompletionDate)
                              : undefined,
                          agency: selectedComplaint.agency,
                          contractorCompanyName:
                            selectedComplaint.contractorCompanyName,
                          contractorEmail: selectedComplaint.contractorEmail,
                          contractorContactNumber:
                            selectedComplaint.contractorContactNumber,
                          location: selectedComplaint.location,
                          mantrapLocation: selectedComplaint.mantrapLocation,
                          noPmaLif: selectedComplaint.noPmaLif,
                          description: selectedComplaint.description,
                          completionNotes: selectedComplaint.completionNotes,
                          contactNumber: selectedComplaint.contactNumber,
                          status:
                            selectedComplaint.status === 'completed'
                              ? ('complete' as const)
                              : selectedComplaint.status === 'in_progress'
                                ? ('in progress' as const)
                                : ('pending' as const),
                        }
                      : undefined
                  }
                  complaintId={isEditMode ? selectedComplaint?.id : undefined}
                />
              </div>
            </div>
          </DrawerContent>
        </Drawer>
      </div>{' '}
      {/* Main Content */}
      {selectedComplaint ? (
        <div className="bg-white rounded-lg border shadow-sm p-6">
          <ComplaintDetails
            complaint={selectedComplaint}
            onClose={handleCloseDetails}
            onEdit={handleEditComplaint}
          />
        </div>
      ) : (
        <div className="bg-white rounded-lg border shadow-sm">
          <ComplaintsTable
            onViewComplaint={handleViewComplaint}
            onEditComplaint={handleEditComplaintFromTable}
          />
        </div>
      )}
    </div>
  );
}
