'use client';

import { Button } from '@/components/ui/button';
import CreateMaintenanceLogForm from '@/features/maintenance-logs/components/CreateMaintenanceLogForm';
import type { CreateMaintenanceLogInput } from '@/features/maintenance-logs/schemas/create-maintenance-log';
import { usePMACertificates } from '@/features/pma-management';
import { useUserWithProfile } from '@/hooks/use-auth';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/lib/supabase';
import { useProjectContext } from '@/providers/project-context';
import { Wrench } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useCallback, useEffect, useState } from 'react';

interface ProjectData {
  contractor_id: string;
  contractor_name: string;
  competent_person_name: string;
  competent_person_phone: string;
}

export default function CreateMaintenanceLogPage() {
  const router = useRouter();
  const { toast } = useToast();
  const { selectedProjectId, isInProjectContext } = useProjectContext();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [projectData, setProjectData] = useState<ProjectData | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Use TanStack Query hook for PMA certificates
  const {
    data: pmaCertificates = [],
    isLoading: _isPMACertificatesLoading,
    error: _pmaCertificatesError,
  } = usePMACertificates(selectedProjectId);

  const { data: user } = useUserWithProfile();

  // Fetch project data including contractor and competent person
  const fetchProjectData = useCallback(async () => {
    if (!selectedProjectId) return;

    setIsLoading(true);
    try {
      // Get project and contractor details
      const { data: project, error: projectError } = await supabase
        .from('projects')
        .select(
          `
                    id,
                    contractor_id,
                    contractors (
                        id,
                        name
                    )
                `,
        )
        .eq('id', selectedProjectId)
        .single();

      if (projectError) throw projectError;

      let competentPersonName = '';
      let competentPersonPhone = '';

      // Try to get competent person from project_users first
      try {
        const { data: competentUser, error: roleError } = await supabase
          .from('project_users')
          .select('user_id')
          .eq('project_id', selectedProjectId)
          .eq('role', 'competent_person')
          .limit(1)
          .maybeSingle();

        if (!roleError && competentUser?.user_id) {
          // Get user details separately to avoid relationship issues
          const { data: userData } = await supabase
            .from('users')
            .select('name, phone_number')
            .eq('id', competentUser.user_id)
            .single();

          if (userData) {
            competentPersonName = userData.name || '';
            competentPersonPhone = userData.phone_number || '';
          }
        }
      } catch {
        console.log(
          'No competent person found in project_users, trying PMA certificates...',
        );
      }

      // If no competent person found in project_users, try getting from PMA certificates
      if (!competentPersonName) {
        try {
          const { data: pmaWithCompetent, error: pmaError } = await supabase
            .from('pma_certificates')
            .select('competent_person_id')
            .eq('project_id', selectedProjectId)
            .not('competent_person_id', 'is', null)
            .limit(1)
            .maybeSingle();

          if (!pmaError && pmaWithCompetent?.competent_person_id) {
            // Get user details separately
            const { data: userData } = await supabase
              .from('users')
              .select('name, phone_number')
              .eq('id', pmaWithCompetent.competent_person_id)
              .single();

            if (userData) {
              competentPersonName = userData.name || '';
              competentPersonPhone = userData.phone_number || '';
            }
          }
        } catch {
          console.log('No competent person found in PMA certificates either');
        }
      }

      setProjectData({
        contractor_id: project.contractor_id || '',
        contractor_name: project.contractors?.name || '',
        competent_person_name: competentPersonName,
        competent_person_phone: competentPersonPhone,
      });
    } catch (error) {
      console.error('Error fetching project data:', error);
      toast({
        title: 'Error',
        description: 'Failed to load project data. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  }, [selectedProjectId, toast]);

  useEffect(() => {
    if (!isInProjectContext) {
      router.push('/projects');
      return;
    }

    fetchProjectData();
  }, [fetchProjectData, isInProjectContext, router]);

  const handleSubmit = async (data: CreateMaintenanceLogInput) => {
    if (!selectedProjectId) return;

    setIsSubmitting(true);
    try {
      // Prepare the data for insertion - only include fields that exist in the database
      const insertData = {
        project_id: selectedProjectId,
        log_date: data.log_date.toISOString().split('T')[0],
        operation_log_type: data.operation_log_type,
        contractor_id: data.contractor_id,
        pma_id: data.pma_id || null,
        description: data.description,
        status: data.status,
        created_by: user?.id || null,
      };

      const { error } = await supabase
        .from('maintenance_logs')
        .insert(insertData);

      if (error) throw error;

      toast({
        title: 'Success',
        description: 'Maintenance log created successfully',
      });

      router.push('/maintenance-logs');
    } catch (error) {
      console.error('Error creating maintenance log:', error);
      toast({
        title: 'Error',
        description: 'Failed to create maintenance log. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isInProjectContext) {
    return null;
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="flex items-center gap-2">
          <div className="animate-spin h-6 w-6 border-2 border-primary border-t-transparent rounded-full" />
          <div className="text-lg">Loading project details...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gradient-to-br from-slate-50 via-white to-slate-50/50">
      {/* Enhanced Header with Breadcrumbs */}
      <div className="sticky top-0 z-40 bg-white/80 backdrop-blur-lg border-b border-slate-200/60">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            {/* Breadcrumbs */}
            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
              <Link
                href="/maintenance-logs"
                className="text-muted-foreground hover:text-foreground transition-colors"
              >
                <div className="flex items-center space-x-2">
                  <Wrench className="h-4 w-4" />
                  <span>Maintenance Logs</span>
                </div>
              </Link>
              <span className="text-muted-foreground">/</span>
              <span className="text-primary font-medium">Create New Log</span>
            </div>

            {/* Actions */}
            <div className="flex items-center space-x-4">
              <Button
                variant="outline"
                onClick={() => router.push('/maintenance-logs')}
              >
                Cancel
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-6 py-8">
        <div className="max-w-2xl mx-auto">
          {/* Header */}
          <div className="mb-8 text-center">
            <h1 className="text-3xl font-bold tracking-tight bg-gradient-to-r from-slate-900 to-slate-700 bg-clip-text text-transparent">
              Create Maintenance Log
            </h1>
            <p className="mt-2 text-lg text-muted-foreground">
              Record a new maintenance activity for your project.
            </p>
          </div>

          {/* Form */}
          <CreateMaintenanceLogForm
            onSubmit={handleSubmit}
            projectData={projectData}
            pmaCertificates={pmaCertificates}
            isSubmitting={isSubmitting}
          />
        </div>
      </div>
    </div>
  );
}
