import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  const requestUrl = new URL(request.url);
  const code = requestUrl.searchParams.get('code');
  const redirectTo = requestUrl.searchParams.get('redirectTo');

  if (code) {
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    try {
      // Exchange code for session
      const { data, error } = await supabase.auth.exchangeCodeForSession(code);

      if (error) {
        console.error('Auth callback error:', error);
        // Redirect to error page or login
        return NextResponse.redirect(
          new URL('/auth/error?error=invalid_code', requestUrl.origin),
        );
      }

      if (data.user) {
        // Check if user profile exists, create if missing
        const { data: existingProfile } = await supabase
          .from('users')
          .select('id, onboarding_completed')
          .eq('id', data.user.id)
          .single();

        if (!existingProfile) {
          // Create profile for new user
          const userRole = data.user.user_metadata?.role || 'contractor';

          const { error: profileError } = await supabase.from('users').insert({
            id: data.user.id,
            email: data.user.email || '',
            name:
              data.user.user_metadata?.full_name ||
              data.user.email?.split('@')[0] ||
              'User',
            phone_number: data.user.user_metadata?.phone_number || null,
            user_role: userRole,
            onboarding_completed: false, // New users need onboarding
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          });

          if (profileError) {
            console.error('Profile creation error:', profileError);
            // Continue anyway - profile can be created later
          }
        }

        // Redirect based on user role and onboarding status
        let finalRedirect = redirectTo;

        if (!finalRedirect) {
          const userRole = data.user.user_metadata?.role || 'contractor';
          const isOnboardingComplete =
            existingProfile?.onboarding_completed || false;

          if (!isOnboardingComplete) {
            // New users need onboarding
            finalRedirect = '/profile';
          } else {
            // Existing users go to dashboard based on role
            finalRedirect =
              userRole === 'admin' ? '/admin/dashboard' : '/dashboard';
          }
        }

        return NextResponse.redirect(new URL(finalRedirect, requestUrl.origin));
      }
    } catch (error) {
      console.error('Unexpected auth callback error:', error);
      return NextResponse.redirect(
        new URL('/auth/error?error=callback_failed', requestUrl.origin),
      );
    }
  }

  // No code provided, redirect to login
  return NextResponse.redirect(new URL('/contractor/login', requestUrl.origin));
}
