import { supabase } from '@/lib/supabase';
import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // Get the current user session to verify authentication
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 },
      );
    }

    const token = authHeader.split(' ')[1];

    // Verify the user token
    const { data: user, error: authError } = await supabase.auth.getUser(token);
    if (authError || !user.user) {
      return NextResponse.json(
        { error: 'Invalid authentication token' },
        { status: 401 },
      );
    }

    const url = new URL(request.url);
    const projectId = url.searchParams.get('projectId');

    if (!projectId) {
      return NextResponse.json(
        { error: 'Missing projectId parameter' },
        { status: 400 },
      );
    }

    // Get user's role in the project
    const { data: projectUser, error: permissionError } = await supabase
      .from('project_users')
      .select('role, is_active, assigned_date, created_at')
      .eq('project_id', projectId)
      .eq('user_id', user.user.id)
      .single();

    if (permissionError) {
      return NextResponse.json({
        userId: user.user.id,
        projectId,
        error: permissionError.message,
        hasProjectAccess: false,
      });
    }

    return NextResponse.json({
      userId: user.user.id,
      projectId,
      projectUser,
      hasProjectAccess: !!projectUser,
      canInvite:
        projectUser?.role === 'admin' ||
        projectUser?.role === 'competent_person',
      allProjectUsers: null, // We'll add this if needed for debugging
    });
  } catch (error) {
    console.error('Error in debug API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 },
    );
  }
}
