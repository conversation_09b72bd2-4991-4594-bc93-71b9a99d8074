import { useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';

/**
 * Hook to handle access control messages
 */
export function useAccessControl() {
  const searchParams = useSearchParams();
  const [accessMessage, setAccessMessage] = useState<string | null>(null);

  useEffect(() => {
    if (!searchParams) return;

    const error = searchParams.get('error');

    if (error === 'access_denied') {
      setAccessMessage('You do not have permission to access that page.');
    } else if (error === 'rbac_error') {
      setAccessMessage(
        'An error occurred while checking your permissions. Please try again.',
      );
    }
  }, [searchParams]);

  /**
   * Clear the access message
   */
  const clearMessage = () => {
    setAccessMessage(null);
  };

  /**
   * Force refresh user role (clears cookie and forces re-fetch)
   */
  const refreshUserRole = () => {
    // Only run on client side
    if (typeof document !== 'undefined' && typeof window !== 'undefined') {
      // Clear role cookie to force refresh on next request
      document.cookie =
        'user_role=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;';
      // Reload the page to trigger middleware refresh
      window.location.reload();
    }
  };

  return {
    accessMessage,
    clearMessage,
    refreshUserRole,
  };
}
