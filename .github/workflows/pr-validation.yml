name: PR Validation

on:
  pull_request:
    branches: [develop, main]

jobs:
  validate:
    name: Build and Test
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20.x'

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 8

      - name: Install dependencies
        run: pnpm install --no-frozen-lockfile

      - name: Create .env.local file
        run: |
          echo "NEXT_PUBLIC_SUPABASE_URL=${{ secrets.STG_SUPABASE_URL }}" >> .env.local
          echo "NEXT_PUBLIC_SUPABASE_ANON_KEY=${{ secrets.STG_SUPABASE_ANON_KEY }}" >> .env.local
          echo "TMONE_BUCKET=simple-obs" >> .env.local
          echo "TMONE_ACCESS_KEY_ID=QN97RS7DLWSLOFSJMSDR" >> .env.local
          echo "TMONE_SECRET_ACCESS_KEY=7FLelJ8IqiwSWKg1LSs8wBJfMsNPRLBccm0OVFl2" >> .env.local
          echo "TMONE_SERVER=https://obs.my-kualalumpur-1.alphaedge.tmone.com.my" >> .env.local
          echo "TMONE_OBS_STORAGE=https://simple-obs.obs.my-kualalumpur-1.alphaedge.tmone.com.my/" >> .env.local
          echo "OBS_EXPIRY_TIME=3600" >> .env.local

      - name: Run TypeScript type checking
        run: pnpm type-check

      - name: Run ESLint
        run: pnpm lint

      - name: Run build
        run: pnpm build

      - name: Check for build artifacts
        run: |
          if [ ! -d "build" ]; then
            echo "❌ Build directory not found!"
            exit 1
          fi
          echo "✅ Build completed successfully!"
