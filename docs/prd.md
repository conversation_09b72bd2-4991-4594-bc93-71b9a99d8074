## Product Requirements Document (PRD)

### 1. Purpose and Objectives

**Purpose:**
This PRD outlines the functional and non-functional requirements for the Lift Management SaaS platform. The system facilitates lift maintenance projects by coordinating contractors, consultants (admins), technicians, competent persons, and building owners (vendors).

**Objectives:**

- Provide a multi-tenant SaaS solution rather than bespoke implementations per client.
- Enable role-based access control across different user types.
- Streamline project creation, team assignment, and complaint resolution workflows.
- Offer real-time monitoring and verification of maintenance activities and complaints.

---

### 2. Scope

**In Scope:**

- User registration and authentication
- Project creation and management by contractors
- Role assignments (admin, technician, competent_person, viewer)
- Invitation workflows for admins and viewers
- Complaint reporting, resolution, and verification
- Monitoring dashboards based on user roles

**Out of Scope (Phase 1):**

- Billing and subscription management
- Detailed analytics beyond basic project KPIs
- Mobile app (web-only initial release)

---

### 3. User Roles & Personas

| Role                   | Description                                                 |
| ---------------------- | ----------------------------------------------------------- |
| **Contractor**         | Maintains lifts; creates projects and invites admins        |
| **Admin (Consultant)** | Approves, monitors, and resolves/ verifies complaints       |
| **Viewer (Vendor)**    | Building owner; reports complaints and views project status |
| **Technician**         | Performs maintenance tasks and reports issues               |
| **Competent Person**   | Monitors maintenance quality, issues PMA certificates       |

---

### 4. Application Flow

#### 4.1 Registration & Onboarding

1. **Admin** registers via sign-up form; selects role `admin` and access mode.
2. **Contractor** registers; submits company details and onboarding documents.
3. **Viewer** accounts are created or invited by admins.

#### 4.2 Project Lifecycle

1. Contractor **creates a new project**, specifying state, location, and timeline.
2. **Admin** discovers the project in dashboard (state-based or invited) and can **join**.
3. Contractor or joined admin can **invite** additional admins and viewers.

#### 4.3 Role Assignment Within Project

- **Technician** and **Competent Person** can be assigned to specific projects by admins or contractors.
- Assignments tracked in `project_users` and `competent_persons` tables.

#### 4.4 Complaint Management

1. **Viewer** or **Technician** submits a complaint via project page.
2. **Admin** reviews complaint, assigns to a technician or competent person.
3. Workflow for **resolve** vs **verify**:

   - Technicians update `maintenance_logs` and mark complaint as resolved.
   - Competent Persons verify repairs and close complaints.

---

### 5. Functional Requirements

#### 5.1 User Management

- **FR-1:** Sign-up, login, password reset
- **FR-2:** Role-based dashboard and navigation
- **FR-3:** Profile management and audit trail

#### 5.2 Project Management

- **FR-4:** Create/Edit/Delete projects
- **FR-5:** Invite and manage project team members (admins, technicians, viewers)
- **FR-6:** State-based and project-based access control via ABAC + RLS

#### 5.3 Complaint Workflow

- **FR-7:** File complaints with descriptions and attachments
- **FR-8:** Assign, resolve, and verify complaints with status updates
- **FR-9:** Notification system (email/in-app) for new and updated complaints

#### 5.4 Certification & Monitoring

- **FR-10:** Upload and manage PMA certificates and LIF lists
- **FR-11:** Competent Person dashboard for expiration alerts
- **FR-12:** Maintenance logs linked to PMA records and projects

---

### 6. Non-functional Requirements

- **NFR-1 (Security):** Implement Supabase RLS for data isolation
- **NFR-2 (Performance):** Page load < 300ms for dashboard screens
- **NFR-3 (Scalability):** Support 10,000+ projects and 100,000+ users
- **NFR-4 (Compliance):** Data residency in Malaysia region
- **NFR-5 (Usability):** Responsive web design; WCAG AA accessibility

---

### 7. Next Steps & Milestones

1. **Week 1–2:** Finalize schema changes, RLS policies, and ABAC implementation
2. **Week 3–4:** Develop core flows: registration, project creation, invitations
3. **Week 5–6:** Build complaint management and monitoring dashboards
4. **Week 7:** QA testing, security audit, and performance tuning
5. **Week 8:** MVP deployment and pilot with first contractor client

---

_End of PRD_
